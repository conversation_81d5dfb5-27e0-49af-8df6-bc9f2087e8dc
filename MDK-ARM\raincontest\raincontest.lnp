--cpu=Cortex-M4.fp.sp
"raincontest\startup_stm32f407xx.o"
"raincontest\main.o"
"raincontest\gpio.o"
"raincontest\dma.o"
"raincontest\i2c.o"
"raincontest\usart.o"
"raincontest\stm32f4xx_it.o"
"raincontest\stm32f4xx_hal_msp.o"
"raincontest\stm32f4xx_hal_i2c.o"
"raincontest\stm32f4xx_hal_i2c_ex.o"
"raincontest\stm32f4xx_hal_rcc.o"
"raincontest\stm32f4xx_hal_rcc_ex.o"
"raincontest\stm32f4xx_hal_flash.o"
"raincontest\stm32f4xx_hal_flash_ex.o"
"raincontest\stm32f4xx_hal_flash_ramfunc.o"
"raincontest\stm32f4xx_hal_gpio.o"
"raincontest\stm32f4xx_hal_dma_ex.o"
"raincontest\stm32f4xx_hal_dma.o"
"raincontest\stm32f4xx_hal_pwr.o"
"raincontest\stm32f4xx_hal_pwr_ex.o"
"raincontest\stm32f4xx_hal_cortex.o"
"raincontest\stm32f4xx_hal.o"
"raincontest\stm32f4xx_hal_exti.o"
"raincontest\stm32f4xx_hal_uart.o"
"raincontest\system_stm32f4xx.o"
"raincontest\key_app.o"
"raincontest\led_app.o"
"raincontest\oled_app.o"
"raincontest\bno08x_app.o"
"raincontest\ringbuffer.o"
"raincontest\hardware_iic.o"
"raincontest\software_iic.o"
"raincontest\led_driver.o"
"raincontest\key_driver.o"
"raincontest\oled.o"
"raincontest\my_scheduler.o"
"raincontest\bno08x_hal.o"
"raincontest\bno08x_hardware_reset_example.o"
--strict --scatter "raincontest\raincontest.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "raincontest.map" -o raincontest\raincontest.axf