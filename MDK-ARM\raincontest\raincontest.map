Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to my_scheduler.o(i.all_task_init) for all_task_init
    main.o(i.main) refers to my_scheduler.o(i.all_task_run) for all_task_run
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for .bss
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for .bss
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.EXTI3_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    key_app.o(i.key_task) refers to key_driver.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to key_app.o(.data) for .data
    key_app.o(i.key_task) refers to led_app.o(.data) for led_rgb
    led_app.o(i.led_task) refers to led_driver.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for .data
    oled_app.o(i.my_oled_init) refers to oled.o(i.OLED_Init) for OLED_Init
    oled_app.o(i.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    oled_app.o(i.oled_task) refers to bno08x_app.o(i.get_roll) for get_roll
    oled_app.o(i.oled_task) refers to bno08x_app.o(i.get_pitch) for get_pitch
    oled_app.o(i.oled_task) refers to bno08x_app.o(i.get_yaw) for get_yaw
    oled_app.o(i.oled_task) refers to oled_app.o(i.__ARM_isnanf) for __ARM_isnanf
    oled_app.o(i.oled_task) refers to oled_app.o(i.oled_printf) for oled_printf
    oled_app.o(i.oled_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.oled_task) refers to oled_app.o(.data) for .data
    oled_app.o(i.oled_task) refers to key_app.o(.data) for count
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.dataAvailable) for dataAvailable
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatI) for getQuatI
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatJ) for getQuatJ
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatK) for getQuatK
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatReal) for getQuatReal
    bno08x_app.o(i.bno080_task) refers to bno08x_app.o(i.__ARM_isnanf) for __ARM_isnanf
    bno08x_app.o(i.bno080_task) refers to bno08x_app.o(i.__ARM_isinff) for __ARM_isinff
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.QuaternionToEulerAngles) for QuaternionToEulerAngles
    bno08x_app.o(i.bno080_task) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.convert_to_continuous_yaw) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.get_pitch) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.get_roll) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.get_yaw) refers to bno08x_app.o(i.convert_to_continuous_yaw) for convert_to_continuous_yaw
    bno08x_app.o(i.get_yaw) refers to bno08x_app.o(.data) for .data
    bno08x_app.o(i.my_bno080_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.BNO080_Init) for BNO080_Init
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.BNO080_HardwareReset) for BNO080_HardwareReset
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.softReset) for softReset
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.enableRotationVector) for enableRotationVector
    bno08x_app.o(i.my_bno080_init) refers to i2c.o(.bss) for hi2c1
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_get) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_init) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_peek) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to abort.o(.text) for abort
    ringbuffer.o(i.rt_ringbuffer_reset) refers to abort.o(.text) for abort
    software_iic.o(i.Delay_us) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    software_iic.o(i.IIC_Anolog_Normalize) refers to software_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    software_iic.o(i.IIC_Get_Anolog) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Digtal) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Offset) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Single_Anolog) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_SendNAck) for IIC_SendNAck
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendNAck) for IIC_SendNAck
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendAck) for IIC_SendAck
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_RecvByte) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    software_iic.o(i.IIC_RecvByte) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    software_iic.o(i.IIC_SendAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendAck) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_SendByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendByte) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_SendByte) refers to software_iic.o(i.IIC_WaitAck) for IIC_WaitAck
    software_iic.o(i.IIC_SendNAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendNAck) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_Start) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_Stop) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_WaitAck) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.Ping) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    led_driver.o(i.led_disp) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(i.led_disp) refers to led_driver.o(.data) for .data
    key_driver.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for .data
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c2
    my_scheduler.o(i.all_task_init) refers to led_app.o(i.led_init) for led_init
    my_scheduler.o(i.all_task_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_scheduler.o(i.all_task_init) refers to oled_app.o(i.my_oled_init) for my_oled_init
    my_scheduler.o(i.all_task_init) refers to bno08x_app.o(i.my_bno080_init) for my_bno080_init
    my_scheduler.o(i.all_task_init) refers to my_scheduler.o(.data) for .data
    my_scheduler.o(i.all_task_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    my_scheduler.o(i.all_task_run) refers to my_scheduler.o(.data) for .data
    my_scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    my_scheduler.o(.data) refers to key_app.o(i.key_task) for key_task
    my_scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    my_scheduler.o(.data) refers to bno08x_app.o(i.bno080_task) for bno080_task
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.BNO080_Init) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    bno08x_hal.o(i.calibrateAccelerometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateAll) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateGyro) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateMagnetometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibratePlanarAccelerometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(i.parseInputReport) for parseInputReport
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.enableAccelerometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableGameRotationVector) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableGyro) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableLinearAccelerometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableMagnetometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableRotationVector) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableStabilityClassifier) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableStepCounter) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.endCalibration) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.frsReadRequest) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.frsReadRequest) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getAccelAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getAccelX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getAccelY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getAccelZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getActivityClassifier) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getGyroZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getLinAccelZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagX) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagY) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getMagZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagZ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQ1) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ1) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getQ2) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ2) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getQ3) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ3) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getQuatAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatI) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatI) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatJ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatJ) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatK) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatK) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatRadianAccuracy) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatRadianAccuracy) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getQuatReal) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatReal) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getRange) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getRange) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getResolution) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getResolution) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.getStabilityClassifier) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.getStepCount) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.parseInputReport) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.parseInputReport) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.qToFloat) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(i.frsReadRequest) for frsReadRequest
    bno08x_hal.o(i.readFRSdata) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.readFRSword) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.readFRSword) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.receivePacket) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    bno08x_hal.o(i.receivePacket) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.receivePacket) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.saveCalibration) refers to bno08x_hal.o(i.sendCommand) for sendCommand
    bno08x_hal.o(i.saveCalibration) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.sendCalibrateCommand) refers to bno08x_hal.o(i.sendCommand) for sendCommand
    bno08x_hal.o(i.sendCalibrateCommand) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.sendPacket) refers to h1_alloc.o(.text) for malloc
    bno08x_hal.o(i.sendPacket) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    bno08x_hal.o(i.sendPacket) refers to h1_free.o(.text) for free
    bno08x_hal.o(i.sendPacket) refers to bno08x_hal.o(.data) for .data
    bno08x_hal.o(i.sendPacket) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.setFeatureCommand) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.setFeatureCommand) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.softReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(.bss) for .bss
    bno08x_hal.o(.data) refers to bno08x_hal.o(.bss) for activityConfidences
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    asinf.o(i.__hardfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__hardfp_asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.__hardfp_asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.__hardfp_asinf) refers to _rserrno.o(.text) for __set_errno
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    asinf.o(i.__softfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf.o(i.asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf_x.o(i.____hardfp_asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.____hardfp_asinf$lsc) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf_x.o(i.____hardfp_asinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf_x.o(i.____hardfp_asinf$lsc) refers to _rserrno.o(.text) for __set_errno
    asinf_x.o(i.____softfp_asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.____softfp_asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    asinf_x.o(i.__asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.__asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    powf.o(i.__hardfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to _rserrno.o(.text) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to _rserrno.o(.text) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000D) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (88 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (120 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (50 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (68 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (560 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (58 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (196 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (552 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (320 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (452 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (184 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (516 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (220 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (208 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (372 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (116 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (236 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (124 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (188 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (16 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (344 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (218 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (182 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (280 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (252 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (348 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.DMA_SetConfig), (40 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT), (110 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (160 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (114 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(.data), (1 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing bno08x_app.o(.rev16_text), (4 bytes).
    Removing bno08x_app.o(.revsh_text), (4 bytes).
    Removing bno08x_app.o(.rrx_text), (6 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_data_len), (48 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_get), (116 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (74 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_init), (48 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (78 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put), (120 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (170 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (80 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (102 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (16 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_status), (32 bytes).
    Removing software_iic.o(.rev16_text), (4 bytes).
    Removing software_iic.o(.revsh_text), (4 bytes).
    Removing software_iic.o(.rrx_text), (6 bytes).
    Removing software_iic.o(i.Delay_us), (76 bytes).
    Removing software_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing software_iic.o(i.IIC_Get_Anolog), (22 bytes).
    Removing software_iic.o(i.IIC_Get_Digtal), (34 bytes).
    Removing software_iic.o(i.IIC_Get_Offset), (24 bytes).
    Removing software_iic.o(i.IIC_Get_Single_Anolog), (22 bytes).
    Removing software_iic.o(i.IIC_ReadByte), (34 bytes).
    Removing software_iic.o(i.IIC_ReadBytes), (100 bytes).
    Removing software_iic.o(i.IIC_RecvByte), (128 bytes).
    Removing software_iic.o(i.IIC_SendAck), (60 bytes).
    Removing software_iic.o(i.IIC_SendByte), (84 bytes).
    Removing software_iic.o(i.IIC_SendNAck), (48 bytes).
    Removing software_iic.o(i.IIC_Start), (64 bytes).
    Removing software_iic.o(i.IIC_Stop), (60 bytes).
    Removing software_iic.o(i.IIC_WaitAck), (64 bytes).
    Removing software_iic.o(i.IIC_WriteByte), (54 bytes).
    Removing software_iic.o(i.IIC_WriteBytes), (72 bytes).
    Removing software_iic.o(i.Ping), (30 bytes).
    Removing led_driver.o(.rev16_text), (4 bytes).
    Removing led_driver.o(.revsh_text), (4 bytes).
    Removing led_driver.o(.rrx_text), (6 bytes).
    Removing key_driver.o(.rev16_text), (4 bytes).
    Removing key_driver.o(.revsh_text), (4 bytes).
    Removing key_driver.o(.rrx_text), (6 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (52 bytes).
    Removing oled.o(i.OLED_Display_Off), (24 bytes).
    Removing oled.o(i.OLED_Display_On), (24 bytes).
    Removing oled.o(i.OLED_ShowFloat), (266 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (76 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (136 bytes).
    Removing oled.o(i.OLED_ShowNum), (114 bytes).
    Removing oled.o(i.OLED_ShowPic), (64 bytes).
    Removing my_scheduler.o(.rev16_text), (4 bytes).
    Removing my_scheduler.o(.revsh_text), (4 bytes).
    Removing my_scheduler.o(.rrx_text), (6 bytes).
    Removing bno08x_hal.o(.rev16_text), (4 bytes).
    Removing bno08x_hal.o(.revsh_text), (4 bytes).
    Removing bno08x_hal.o(.rrx_text), (6 bytes).
    Removing bno08x_hal.o(i.calibrateAccelerometer), (6 bytes).
    Removing bno08x_hal.o(i.calibrateAll), (6 bytes).
    Removing bno08x_hal.o(i.calibrateGyro), (6 bytes).
    Removing bno08x_hal.o(i.calibrateMagnetometer), (6 bytes).
    Removing bno08x_hal.o(i.calibratePlanarAccelerometer), (6 bytes).
    Removing bno08x_hal.o(i.enableAccelerometer), (10 bytes).
    Removing bno08x_hal.o(i.enableGameRotationVector), (10 bytes).
    Removing bno08x_hal.o(i.enableGyro), (10 bytes).
    Removing bno08x_hal.o(i.enableLinearAccelerometer), (10 bytes).
    Removing bno08x_hal.o(i.enableMagnetometer), (10 bytes).
    Removing bno08x_hal.o(i.enableStabilityClassifier), (10 bytes).
    Removing bno08x_hal.o(i.enableStepCounter), (10 bytes).
    Removing bno08x_hal.o(i.endCalibration), (6 bytes).
    Removing bno08x_hal.o(i.frsReadRequest), (44 bytes).
    Removing bno08x_hal.o(i.getAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getAccelX), (20 bytes).
    Removing bno08x_hal.o(i.getAccelY), (20 bytes).
    Removing bno08x_hal.o(i.getAccelZ), (20 bytes).
    Removing bno08x_hal.o(i.getActivityClassifier), (12 bytes).
    Removing bno08x_hal.o(i.getGyroAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getGyroX), (20 bytes).
    Removing bno08x_hal.o(i.getGyroY), (20 bytes).
    Removing bno08x_hal.o(i.getGyroZ), (20 bytes).
    Removing bno08x_hal.o(i.getLinAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getLinAccelX), (20 bytes).
    Removing bno08x_hal.o(i.getLinAccelY), (20 bytes).
    Removing bno08x_hal.o(i.getLinAccelZ), (20 bytes).
    Removing bno08x_hal.o(i.getMagAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getMagX), (20 bytes).
    Removing bno08x_hal.o(i.getMagY), (20 bytes).
    Removing bno08x_hal.o(i.getMagZ), (20 bytes).
    Removing bno08x_hal.o(i.getQ1), (28 bytes).
    Removing bno08x_hal.o(i.getQ2), (28 bytes).
    Removing bno08x_hal.o(i.getQ3), (28 bytes).
    Removing bno08x_hal.o(i.getQuatAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getQuatRadianAccuracy), (20 bytes).
    Removing bno08x_hal.o(i.getRange), (36 bytes).
    Removing bno08x_hal.o(i.getResolution), (36 bytes).
    Removing bno08x_hal.o(i.getStabilityClassifier), (12 bytes).
    Removing bno08x_hal.o(i.getStepCount), (12 bytes).
    Removing bno08x_hal.o(i.readFRSdata), (128 bytes).
    Removing bno08x_hal.o(i.readFRSword), (24 bytes).
    Removing bno08x_hal.o(i.resetReason), (48 bytes).
    Removing bno08x_hal.o(i.saveCalibration), (28 bytes).
    Removing bno08x_hal.o(i.sendCalibrateCommand), (64 bytes).
    Removing bno08x_hal.o(i.sendCommand), (48 bytes).

427 unused section(s) (total 28517 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf_x.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\User\my_scheduler.c                   0x00000000   Number         0  my_scheduler.o ABSOLUTE
    ..\\User\\my_scheduler.c                 0x00000000   Number         0  my_scheduler.o ABSOLUTE
    ..\\code\\Driver\\Key\\key_driver.c      0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\\code\\Driver\\Led\\led_driver.c      0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\\code\\Driver\\bno08x\\bno08x_hal.c   0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    ..\\code\\Driver\\grayscale\\software_iic.c 0x00000000   Number         0  software_iic.o ABSOLUTE
    ..\\code\\Driver\\oled\\oled.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\\code\\Myapp\\bno08x_app.c            0x00000000   Number         0  bno08x_app.o ABSOLUTE
    ..\\code\\Myapp\\key_app.c               0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\code\\Myapp\\led_app.c               0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\code\\Myapp\\oled_app.c              0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\code\Driver\Key\key_driver.c          0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\code\Driver\Led\led_driver.c          0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\code\Driver\bno08x\bno08x_hal.c       0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    ..\code\Driver\bno08x\bno08x_hardware_reset_example.c 0x00000000   Number         0  bno08x_hardware_reset_example.o ABSOLUTE
    ..\code\Driver\grayscale\hardware_iic.c  0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\code\Driver\grayscale\software_iic.c  0x00000000   Number         0  software_iic.o ABSOLUTE
    ..\code\Driver\oled\oled.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\code\Driver\ringbuffer\ringbuffer.c   0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\code\Myapp\bno08x_app.c               0x00000000   Number         0  bno08x_app.o ABSOLUTE
    ..\code\Myapp\key_app.c                  0x00000000   Number         0  key_app.o ABSOLUTE
    ..\code\Myapp\led_app.c                  0x00000000   Number         0  led_app.o ABSOLUTE
    ..\code\Myapp\oled_app.c                 0x00000000   Number         0  oled_app.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x08000292   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x0800029a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x0800029a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800029a   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x0800029a   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002a0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002a0   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002ac   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002ac   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002ac   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002b6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002b6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080002ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080002ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080002ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080002ba   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080002ba   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080002bc   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002bc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002bc   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002c2   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002c2   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002c6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002c6   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002ce   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002d0   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002d0   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002d4   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x080002dc   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x080002dc   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002dc   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800031c   Section        0  h1_alloc.o(.text)
    .text                                    0x0800037a   Section        0  h1_free.o(.text)
    .text                                    0x080003c8   Section      238  lludivv7m.o(.text)
    .text                                    0x080004b8   Section        0  vsnprintf.o(.text)
    .text                                    0x080004ec   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800053a   Section        0  heapauxi.o(.text)
    .text                                    0x08000540   Section        0  sys_exit.o(.text)
    .text                                    0x0800054c   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x08000554   Section        0  hguard.o(.text)
    .text                                    0x08000558   Section        0  init_alloc.o(.text)
    .text                                    0x080005e2   Section        0  h1_init.o(.text)
    .text                                    0x080005f0   Section        0  _rserrno.o(.text)
    .text                                    0x08000606   Section        0  _printf_pad.o(.text)
    .text                                    0x08000654   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000678   Section        0  _printf_str.o(.text)
    .text                                    0x080006cc   Section        0  _printf_dec.o(.text)
    .text                                    0x08000744   Section        0  _printf_charcount.o(.text)
    .text                                    0x0800076c   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x0800076d   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x0800079c   Section        0  _sputc.o(.text)
    .text                                    0x080007a6   Section        0  _snputc.o(.text)
    .text                                    0x080007b8   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000874   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x080008f0   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x080008f1   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000960   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000961   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x080009f4   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000b7c   Section        8  libspace.o(.text)
    .text                                    0x08000b84   Section        2  use_no_semi.o(.text)
    .text                                    0x08000b86   Section        0  indicate_semi.o(.text)
    .text                                    0x08000b88   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000b90   Section        0  h1_extend.o(.text)
    .text                                    0x08000bc4   Section      138  lludiv10.o(.text)
    .text                                    0x08000c4e   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000d00   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000d03   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001120   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x0800141c   Section        0  _printf_char.o(.text)
    .text                                    0x08001448   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001474   Section        0  _wcrtomb.o(.text)
    .text                                    0x080014b4   Section        0  defsig_exit.o(.text)
    .text                                    0x080014be   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x080014cc   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001518   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001528   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001530   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080015b0   Section        0  bigflt0.o(.text)
    .text                                    0x08001694   Section        0  exit.o(.text)
    .text                                    0x080016a6   Section        0  defsig_general.o(.text)
    .text                                    0x080016d8   Section        0  defsig_rtmem_inner.o(.text)
    .text                                    0x08001728   Section        0  sys_wrch.o(.text)
    .text                                    0x08001738   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x080017b8   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080017f6   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800183c   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800189c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08001bd4   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001cb0   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001cda   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001d04   Section      580  btod.o(CL$$btod_mult_common)
    i.BNO080_HardwareReset                   0x08001f48   Section        0  bno08x_hal.o(i.BNO080_HardwareReset)
    i.BNO080_Init                            0x08001fbc   Section        0  bno08x_hal.o(i.BNO080_Init)
    i.BusFault_Handler                       0x08001fc8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Stream0_IRQHandler                0x08001fcc   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08001fd8   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08001fe4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08001fe5   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x0800200c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x0800200d   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DebugMon_Handler                       0x08002060   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.EXTI3_IRQHandler                       0x08002062   Section        0  stm32f4xx_it.o(i.EXTI3_IRQHandler)
    i.Error_Handler                          0x08002068   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x0800206c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080020fe   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002124   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x080022c4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_Delay                              0x08002398   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x080023bc   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080023c0   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080023d8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080025c8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080025d2   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x080025dc   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x080025e8   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Receive                 0x08002770   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    i.HAL_I2C_Master_Transmit                0x08002960   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_Mem_Write                      0x08002a8c   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08002bbc   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08002c68   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002c78   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002cac   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002cec   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002d1c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002d38   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002d78   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002d9c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08002ed0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08002ef0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08002f10   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08002f70   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080032dc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UARTEx_RxEventCallback             0x08003304   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x08003306   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x08003308   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x08003588   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x080035ec   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08003750   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_TxCpltCallback                0x08003752   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08003754   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08003756   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08003757   Thumb Code    46  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterRequestRead                  0x08003784   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead)
    I2C_MasterRequestRead                    0x08003785   Thumb Code   230  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead)
    i.I2C_MasterRequestWrite                 0x08003870   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x08003871   Thumb Code   150  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_RequestMemoryWrite                 0x0800390c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x0800390d   Thumb Code   162  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x080039b4   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x080039b5   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08003a0c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08003a0d   Thumb Code   144  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08003a9c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08003a9d   Thumb Code   188  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08003b58   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08003b59   Thumb Code   112  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08003bc8   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08003bc9   Thumb Code    86  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.MX_DMA_Init                            0x08003c20   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08003c6c   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x08003dd8   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x08003e18   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_UART5_Init                          0x08003e58   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x08003e90   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08003ec8   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08003eca   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08003ecc   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08003f00   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08003f30   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x08003f54   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x08003fdc   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x08004014   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08004038   Section        0  oled.o(i.OLED_Write_data)
    i.PendSV_Handler                         0x0800405c   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.QuaternionToEulerAngles                0x08004060   Section        0  bno08x_hal.o(i.QuaternionToEulerAngles)
    i.SVC_Handler                            0x08004168   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x0800416a   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08004170   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08004204   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.UART5_IRQHandler                       0x08004214   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x08004220   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08004221   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x0800422e   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x0800422f   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x0800427c   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x0800427d   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08004340   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08004341   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.USART1_IRQHandler                      0x0800444c   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08004458   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x0800445a   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x0800448a   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__ARM_isinff                           0x080044b0   Section        0  bno08x_app.o(i.__ARM_isinff)
    __ARM_isinff                             0x080044b1   Thumb Code    18  bno08x_app.o(i.__ARM_isinff)
    i.__ARM_isnanf                           0x080044c2   Section        0  oled_app.o(i.__ARM_isnanf)
    __ARM_isnanf                             0x080044c3   Thumb Code    12  oled_app.o(i.__ARM_isnanf)
    i.__ARM_isnanf                           0x080044ce   Section        0  bno08x_app.o(i.__ARM_isnanf)
    __ARM_isnanf                             0x080044cf   Thumb Code    12  bno08x_app.o(i.__ARM_isnanf)
    i.__NVIC_SetPriority                     0x080044da   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080044db   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_asinf                         0x080044fc   Section        0  asinf.o(i.__hardfp_asinf)
    i.__hardfp_atan2f                        0x08004628   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_powf                          0x08004878   Section        0  powf.o(i.__hardfp_powf)
    i.__hardfp_sqrtf                         0x08004edc   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x08004f18   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08004f2c   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x08004f32   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x08004f38   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_overflow                 0x08004f48   Section        0  funder.o(i.__mathlib_flt_overflow)
    i.__mathlib_flt_underflow                0x08004f58   Section        0  funder.o(i.__mathlib_flt_underflow)
    i._is_digit                              0x08004f68   Section        0  __printf_wp.o(i._is_digit)
    i.all_task_init                          0x08004f78   Section        0  my_scheduler.o(i.all_task_init)
    i.all_task_run                           0x08004fa8   Section        0  my_scheduler.o(i.all_task_run)
    i.bno080_task                            0x08004fe4   Section        0  bno08x_app.o(i.bno080_task)
    i.convert_to_continuous_yaw              0x080050bc   Section        0  bno08x_app.o(i.convert_to_continuous_yaw)
    i.dataAvailable                          0x08005120   Section        0  bno08x_hal.o(i.dataAvailable)
    i.enableRotationVector                   0x08005150   Section        0  bno08x_hal.o(i.enableRotationVector)
    i.getQuatI                               0x0800515c   Section        0  bno08x_hal.o(i.getQuatI)
    i.getQuatJ                               0x08005170   Section        0  bno08x_hal.o(i.getQuatJ)
    i.getQuatK                               0x08005184   Section        0  bno08x_hal.o(i.getQuatK)
    i.getQuatReal                            0x08005198   Section        0  bno08x_hal.o(i.getQuatReal)
    i.get_pitch                              0x080051ac   Section        0  bno08x_app.o(i.get_pitch)
    i.get_roll                               0x080051c8   Section        0  bno08x_app.o(i.get_roll)
    i.get_yaw                                0x080051e4   Section        0  bno08x_app.o(i.get_yaw)
    i.key_read                               0x08005200   Section        0  key_driver.o(i.key_read)
    i.key_task                               0x08005250   Section        0  key_app.o(i.key_task)
    i.led_disp                               0x0800529c   Section        0  led_driver.o(i.led_disp)
    i.led_init                               0x0800531c   Section        0  led_app.o(i.led_init)
    i.led_task                               0x08005320   Section        0  led_app.o(i.led_task)
    i.main                                   0x0800532c   Section        0  main.o(i.main)
    i.my_bno080_init                         0x08005358   Section        0  bno08x_app.o(i.my_bno080_init)
    i.my_oled_init                           0x080053a4   Section        0  oled_app.o(i.my_oled_init)
    i.oled_printf                            0x080053a8   Section        0  oled_app.o(i.oled_printf)
    i.oled_task                              0x080053f8   Section        0  oled_app.o(i.oled_task)
    i.parseInputReport                       0x080055d8   Section        0  bno08x_hal.o(i.parseInputReport)
    i.qToFloat                               0x080056b0   Section        0  bno08x_hal.o(i.qToFloat)
    i.receivePacket                          0x080056dc   Section        0  bno08x_hal.o(i.receivePacket)
    receivePacket                            0x080056dd   Thumb Code   144  bno08x_hal.o(i.receivePacket)
    i.sendPacket                             0x08005774   Section        0  bno08x_hal.o(i.sendPacket)
    sendPacket                               0x08005775   Thumb Code   102  bno08x_hal.o(i.sendPacket)
    i.setFeatureCommand                      0x080057e4   Section        0  bno08x_hal.o(i.setFeatureCommand)
    i.softReset                              0x08005830   Section        0  bno08x_hal.o(i.softReset)
    i.sqrtf                                  0x08005864   Section        0  sqrtf.o(i.sqrtf)
    locale$$code                             0x080058a4   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080058d0   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dretinf                            0x080058fc   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x080058fc   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08005908   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08005908   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x0800595e   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x0800595e   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x080059ea   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x080059ea   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x080059f4   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x080059f4   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x080059f8   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x080059f8   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x080059fc   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x080059fc   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x080059fc   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x08005a04   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005a14   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08005a1c   Section     2712  oled.o(.constdata)
    F8X16                                    0x08005c44   Data        1520  oled.o(.constdata)
    .constdata                               0x080064b4   Section      320  powf.o(.constdata)
    table                                    0x080064b4   Data         128  powf.o(.constdata)
    powersof2to1over16top                    0x08006534   Data          64  powf.o(.constdata)
    powersof2to1over16bot                    0x08006574   Data          64  powf.o(.constdata)
    powersof2to1over16all                    0x080065b4   Data          64  powf.o(.constdata)
    .constdata                               0x080065f4   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x080065f4   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x080065fc   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x080065fc   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x08006610   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08006624   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08006624   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08006635   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08006635   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08006648   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800665c   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800665c   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x08006698   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x08006710   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08006714   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800671c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08006728   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800672a   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800672b   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800672c   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800672c   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x08006730   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08006738   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800683c   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section        5  key_app.o(.data)
    key_old                                  0x20000010   Data           1  key_app.o(.data)
    .data                                    0x20000015   Section        5  led_app.o(.data)
    .data                                    0x2000001c   Section        8  oled_app.o(.data)
    test_mode                                0x2000001c   Data           1  oled_app.o(.data)
    last_switch                              0x20000020   Data           4  oled_app.o(.data)
    .data                                    0x20000024   Section       28  bno08x_app.o(.data)
    .data                                    0x20000040   Section        1  led_driver.o(.data)
    temp_old                                 0x20000040   Data           1  led_driver.o(.data)
    .data                                    0x20000041   Section       22  oled.o(.data)
    .data                                    0x20000058   Section       52  my_scheduler.o(.data)
    .data                                    0x2000008c   Section       86  bno08x_hal.o(.data)
    commandSequenceNumber                    0x2000008c   Data           1  bno08x_hal.o(.data)
    _deviceAddress                           0x2000008d   Data           1  bno08x_hal.o(.data)
    accelAccuracy                            0x2000008e   Data           1  bno08x_hal.o(.data)
    accelLinAccuracy                         0x2000008f   Data           1  bno08x_hal.o(.data)
    gyroAccuracy                             0x20000090   Data           1  bno08x_hal.o(.data)
    magAccuracy                              0x20000091   Data           1  bno08x_hal.o(.data)
    quatAccuracy                             0x20000092   Data           1  bno08x_hal.o(.data)
    stabilityClassifier                      0x20000093   Data           1  bno08x_hal.o(.data)
    activityClassifier                       0x20000094   Data           1  bno08x_hal.o(.data)
    rawAccelX                                0x20000096   Data           2  bno08x_hal.o(.data)
    rawAccelY                                0x20000098   Data           2  bno08x_hal.o(.data)
    rawAccelZ                                0x2000009a   Data           2  bno08x_hal.o(.data)
    rawLinAccelX                             0x2000009c   Data           2  bno08x_hal.o(.data)
    rawLinAccelY                             0x2000009e   Data           2  bno08x_hal.o(.data)
    rawLinAccelZ                             0x200000a0   Data           2  bno08x_hal.o(.data)
    rawGyroX                                 0x200000a2   Data           2  bno08x_hal.o(.data)
    rawGyroY                                 0x200000a4   Data           2  bno08x_hal.o(.data)
    rawGyroZ                                 0x200000a6   Data           2  bno08x_hal.o(.data)
    rawMagX                                  0x200000a8   Data           2  bno08x_hal.o(.data)
    rawMagY                                  0x200000aa   Data           2  bno08x_hal.o(.data)
    rawMagZ                                  0x200000ac   Data           2  bno08x_hal.o(.data)
    rawQuatI                                 0x200000ae   Data           2  bno08x_hal.o(.data)
    rawQuatJ                                 0x200000b0   Data           2  bno08x_hal.o(.data)
    rawQuatK                                 0x200000b2   Data           2  bno08x_hal.o(.data)
    rawQuatReal                              0x200000b4   Data           2  bno08x_hal.o(.data)
    rawQuatRadianAccuracy                    0x200000b6   Data           2  bno08x_hal.o(.data)
    stepCount                                0x200000b8   Data           2  bno08x_hal.o(.data)
    _activityConfidences                     0x200000bc   Data           4  bno08x_hal.o(.data)
    rotationVector_Q1                        0x200000c0   Data           4  bno08x_hal.o(.data)
    accelerometer_Q1                         0x200000c4   Data           4  bno08x_hal.o(.data)
    linear_accelerometer_Q1                  0x200000c8   Data           4  bno08x_hal.o(.data)
    gyro_Q1                                  0x200000cc   Data           4  bno08x_hal.o(.data)
    magnetometer_Q1                          0x200000d0   Data           4  bno08x_hal.o(.data)
    hi2c_bno080                              0x200000d4   Data           4  bno08x_hal.o(.data)
    shtpHeader                               0x200000d8   Data           4  bno08x_hal.o(.data)
    sequenceNumber                           0x200000dc   Data           6  bno08x_hal.o(.data)
    .bss                                     0x200000e4   Section      168  i2c.o(.bss)
    .bss                                     0x2000018c   Section      336  usart.o(.bss)
    .bss                                     0x200002dc   Section      164  bno08x_hal.o(.bss)
    shtpData                                 0x200002dc   Data         128  bno08x_hal.o(.bss)
    metaData                                 0x2000035c   Data          36  bno08x_hal.o(.bss)
    .bss                                     0x20000380   Section       10  bno08x_hal.o(.bss)
    activityConfidences                      0x20000380   Data          10  bno08x_hal.o(.bss)
    .bss                                     0x2000038c   Section       96  libspace.o(.bss)
    HEAP                                     0x200003f0   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x200003f0   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x200005f0   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x200005f0   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x200009f0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_heap_1                     0x0800029b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x0800029b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x0800029b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x0800029b   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002a1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002a1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002ad   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002ad   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002ad   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002b7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080002bb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080002bd   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002bd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002bd   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002c3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002c3   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002cf   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002d1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002d1   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002d5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002dd   Thumb Code     8  startup_stm32f407xx.o(.text)
    _maybe_terminate_alloc                   0x080002dd   Thumb Code     0  maybetermalloc1.o(.emb_text)
    ADC_IRQHandler                           0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002f7   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002f9   Thumb Code     0  startup_stm32f407xx.o(.text)
    malloc                                   0x0800031d   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x0800037b   Thumb Code    78  h1_free.o(.text)
    __aeabi_uldivmod                         0x080003c9   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x080003c9   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x080004b9   Thumb Code    48  vsnprintf.o(.text)
    __aeabi_memclr4                          0x080004ed   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080004ed   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080004ed   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080004f1   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800053b   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x0800053d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x0800053f   Thumb Code     2  heapauxi.o(.text)
    _sys_exit                                0x08000541   Thumb Code     8  sys_exit.o(.text)
    __rt_heap_descriptor                     0x0800054d   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x08000555   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x08000557   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08000559   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x0800055b   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x0800055d   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x0800057f   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x08000585   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x080005e3   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x080005ed   Thumb Code     4  h1_init.o(.text)
    __read_errno                             0x080005f1   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080005fb   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08000607   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08000633   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x08000655   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x08000667   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000679   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x080006cd   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000745   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000777   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x0800079d   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x080007a7   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x080007b9   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000875   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x080008f1   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000933   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x0800094b   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000961   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x080009b7   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x080009d3   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x080009df   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x080009f5   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __user_libspace                          0x08000b7d   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000b7d   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000b7d   Thumb Code     0  libspace.o(.text)
    __I$use$semihosting                      0x08000b85   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08000b85   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08000b87   Thumb Code     0  indicate_semi.o(.text)
    __aeabi_errno_addr                       0x08000b89   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000b89   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000b89   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __Heap_ProvideMemory                     0x08000b91   Thumb Code    52  h1_extend.o(.text)
    _ll_udiv10                               0x08000bc5   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000c4f   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000d01   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000eb3   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08001121   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x0800141d   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001431   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001441   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001449   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x0800145d   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x0800146d   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x08001475   Thumb Code    64  _wcrtomb.o(.text)
    __sig_exit                               0x080014b5   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGRTMEM                            0x080014bf   Thumb Code    14  defsig_rtmem_outer.o(.text)
    __user_setup_stackheap                   0x080014cd   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001519   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001529   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001531   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080015b1   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001695   Thumb Code    18  exit.o(.text)
    __default_signal_display                 0x080016a7   Thumb Code    50  defsig_general.o(.text)
    __rt_SIGRTMEM_inner                      0x080016d9   Thumb Code    22  defsig_rtmem_inner.o(.text)
    _ttywrch                                 0x08001729   Thumb Code    14  sys_wrch.o(.text)
    strcmp                                   0x08001739   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x080017b9   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080017f7   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800183d   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800189d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08001bd5   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001cb1   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001cdb   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001d05   Thumb Code   580  btod.o(CL$$btod_mult_common)
    BNO080_HardwareReset                     0x08001f49   Thumb Code   106  bno08x_hal.o(i.BNO080_HardwareReset)
    BNO080_Init                              0x08001fbd   Thumb Code     8  bno08x_hal.o(i.BNO080_Init)
    BusFault_Handler                         0x08001fc9   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Stream0_IRQHandler                  0x08001fcd   Thumb Code     6  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08001fd9   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08002061   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    EXTI3_IRQHandler                         0x08002063   Thumb Code     6  stm32f4xx_it.o(i.EXTI3_IRQHandler)
    Error_Handler                            0x08002069   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x0800206d   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080020ff   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002125   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x080022c5   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_Delay                                0x08002399   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x080023bd   Thumb Code     2  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080023c1   Thumb Code    18  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080023d9   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080025c9   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080025d3   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x080025dd   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x080025e9   Thumb Code   376  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Receive                   0x08002771   Thumb Code   482  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    HAL_I2C_Master_Transmit                  0x08002961   Thumb Code   290  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_Mem_Write                        0x08002a8d   Thumb Code   294  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08002bbd   Thumb Code   154  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08002c69   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002c79   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002cad   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002ced   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002d1d   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002d39   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002d79   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002d9d   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08002ed1   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08002ef1   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08002f11   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08002f71   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080032dd   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UARTEx_RxEventCallback               0x08003305   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x08003307   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x08003309   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x08003589   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x080035ed   Thumb Code   320  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08003751   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_TxCpltCallback                  0x08003753   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08003755   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_DMA_Init                              0x08003c21   Thumb Code    72  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08003c6d   Thumb Code   344  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x08003dd9   Thumb Code    50  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x08003e19   Thumb Code    50  i2c.o(i.MX_I2C2_Init)
    MX_UART5_Init                            0x08003e59   Thumb Code    48  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x08003e91   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08003ec9   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08003ecb   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08003ecd   Thumb Code    52  oled.o(i.OLED_Clear)
    OLED_Init                                0x08003f01   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08003f31   Thumb Code    34  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x08003f55   Thumb Code   126  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x08003fdd   Thumb Code    54  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x08004015   Thumb Code    30  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08004039   Thumb Code    30  oled.o(i.OLED_Write_data)
    PendSV_Handler                           0x0800405d   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    QuaternionToEulerAngles                  0x08004061   Thumb Code   252  bno08x_hal.o(i.QuaternionToEulerAngles)
    SVC_Handler                              0x08004169   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x0800416b   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08004171   Thumb Code   138  main.o(i.SystemClock_Config)
    SystemInit                               0x08004205   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    UART5_IRQHandler                         0x08004215   Thumb Code     6  stm32f4xx_it.o(i.UART5_IRQHandler)
    USART1_IRQHandler                        0x0800444d   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08004459   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x0800445b   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x0800448b   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_asinf                           0x080044fd   Thumb Code   258  asinf.o(i.__hardfp_asinf)
    __hardfp_atan2f                          0x08004629   Thumb Code   502  atan2f.o(i.__hardfp_atan2f)
    __hardfp_powf                            0x08004879   Thumb Code  1606  powf.o(i.__hardfp_powf)
    __mathlib_powf                           0x08004879   Thumb Code     0  powf.o(i.__hardfp_powf)
    __hardfp_sqrtf                           0x08004edd   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x08004f19   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08004f2d   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x08004f33   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x08004f39   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_overflow                   0x08004f49   Thumb Code    10  funder.o(i.__mathlib_flt_overflow)
    __mathlib_flt_underflow                  0x08004f59   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    _is_digit                                0x08004f69   Thumb Code    14  __printf_wp.o(i._is_digit)
    all_task_init                            0x08004f79   Thumb Code    42  my_scheduler.o(i.all_task_init)
    all_task_run                             0x08004fa9   Thumb Code    56  my_scheduler.o(i.all_task_run)
    bno080_task                              0x08004fe5   Thumb Code   210  bno08x_app.o(i.bno080_task)
    convert_to_continuous_yaw                0x080050bd   Thumb Code    82  bno08x_app.o(i.convert_to_continuous_yaw)
    dataAvailable                            0x08005121   Thumb Code    38  bno08x_hal.o(i.dataAvailable)
    enableRotationVector                     0x08005151   Thumb Code    10  bno08x_hal.o(i.enableRotationVector)
    getQuatI                                 0x0800515d   Thumb Code    14  bno08x_hal.o(i.getQuatI)
    getQuatJ                                 0x08005171   Thumb Code    14  bno08x_hal.o(i.getQuatJ)
    getQuatK                                 0x08005185   Thumb Code    14  bno08x_hal.o(i.getQuatK)
    getQuatReal                              0x08005199   Thumb Code    14  bno08x_hal.o(i.getQuatReal)
    get_pitch                                0x080051ad   Thumb Code    18  bno08x_app.o(i.get_pitch)
    get_roll                                 0x080051c9   Thumb Code    18  bno08x_app.o(i.get_roll)
    get_yaw                                  0x080051e5   Thumb Code    20  bno08x_app.o(i.get_yaw)
    key_read                                 0x08005201   Thumb Code    70  key_driver.o(i.key_read)
    key_task                                 0x08005251   Thumb Code    68  key_app.o(i.key_task)
    led_disp                                 0x0800529d   Thumb Code   116  led_driver.o(i.led_disp)
    led_init                                 0x0800531d   Thumb Code     2  led_app.o(i.led_init)
    led_task                                 0x08005321   Thumb Code     6  led_app.o(i.led_task)
    main                                     0x0800532d   Thumb Code    42  main.o(i.main)
    my_bno080_init                           0x08005359   Thumb Code    72  bno08x_app.o(i.my_bno080_init)
    my_oled_init                             0x080053a5   Thumb Code     4  oled_app.o(i.my_oled_init)
    oled_printf                              0x080053a9   Thumb Code    74  oled_app.o(i.oled_printf)
    oled_task                                0x080053f9   Thumb Code   306  oled_app.o(i.oled_task)
    parseInputReport                         0x080055d9   Thumb Code   208  bno08x_hal.o(i.parseInputReport)
    qToFloat                                 0x080056b1   Thumb Code    42  bno08x_hal.o(i.qToFloat)
    setFeatureCommand                        0x080057e5   Thumb Code    70  bno08x_hal.o(i.setFeatureCommand)
    softReset                                0x08005831   Thumb Code    46  bno08x_hal.o(i.softReset)
    sqrtf                                    0x08005865   Thumb Code    62  sqrtf.o(i.sqrtf)
    _get_lc_numeric                          0x080058a5   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080058d1   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __fpl_dretinf                            0x080058fd   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08005909   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08005909   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x0800595f   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x080059eb   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x080059f3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x080059f3   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x080059f5   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x080059f9   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x080059fc   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x08005a04   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x08005a14   Data           8  system_stm32f4xx.o(.constdata)
    F6X8                                     0x08005a1c   Data         552  oled.o(.constdata)
    Hzk                                      0x08006234   Data         128  oled.o(.constdata)
    Hzb                                      0x080062b4   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x080066f0   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08006710   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x08006739   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    count                                    0x20000011   Data           1  key_app.o(.data)
    key_val                                  0x20000012   Data           1  key_app.o(.data)
    key_down                                 0x20000013   Data           1  key_app.o(.data)
    key_up                                   0x20000014   Data           1  key_app.o(.data)
    led_rgb                                  0x20000015   Data           5  led_app.o(.data)
    first_flat                               0x20000024   Data           1  bno08x_app.o(.data)
    g_is_yaw_initialized                     0x20000025   Data           1  bno08x_app.o(.data)
    first_yaw                                0x20000028   Data           4  bno08x_app.o(.data)
    g_last_yaw                               0x2000002c   Data           4  bno08x_app.o(.data)
    g_revolution_count                       0x20000030   Data           4  bno08x_app.o(.data)
    roll                                     0x20000034   Data           4  bno08x_app.o(.data)
    pitch                                    0x20000038   Data           4  bno08x_app.o(.data)
    yaw                                      0x2000003c   Data           4  bno08x_app.o(.data)
    initcmd1                                 0x20000041   Data          22  oled.o(.data)
    task_num                                 0x20000058   Data           1  my_scheduler.o(.data)
    all_task                                 0x2000005c   Data          48  my_scheduler.o(.data)
    hi2c1                                    0x200000e4   Data          84  i2c.o(.bss)
    hi2c2                                    0x20000138   Data          84  i2c.o(.bss)
    huart5                                   0x2000018c   Data          72  usart.o(.bss)
    huart1                                   0x200001d4   Data          72  usart.o(.bss)
    hdma_uart5_rx                            0x2000021c   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x2000027c   Data          96  usart.o(.bss)
    __libspace_start                         0x2000038c   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200003ec   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00006920, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000683c, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         3594  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         4056    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         4058    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         4060    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         3767    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         3756    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         3758    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         3763    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         3764    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         3765    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         3766    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         3771    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         3760    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         3761    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         3762    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         3759    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         3757    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         3768    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         3769    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         3770    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         3775    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         3776    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         3772    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         3754    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         3755    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         3773    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         3774    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         3916    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         3996    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         3785    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         3788    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000008   Code   RO         3789    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x0800029a   0x0800029a   0x00000000   Code   RO         3791    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x0800029a   0x0800029a   0x00000000   Code   RO         3793    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800029a   0x0800029a   0x00000000   Code   RO         3795    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         3796    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002a0   0x080002a0   0x00000000   Code   RO         3798    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002a0   0x080002a0   0x0000000c   Code   RO         3799    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002ac   0x080002ac   0x00000000   Code   RO         3800    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002ac   0x080002ac   0x00000000   Code   RO         3802    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002ac   0x080002ac   0x0000000a   Code   RO         3803    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3804    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3806    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3808    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3810    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3812    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3814    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3816    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3818    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3822    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3824    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3826    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000000   Code   RO         3828    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002b6   0x080002b6   0x00000002   Code   RO         3829    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b8   0x080002b8   0x00000002   Code   RO         4053    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4017    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4019    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4022    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4025    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4027    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4030    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002ba   0x080002ba   0x00000002   Code   RO         4031    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080002bc   0x080002bc   0x00000000   Code   RO         3652    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002bc   0x080002bc   0x00000000   Code   RO         3870    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002bc   0x080002bc   0x00000006   Code   RO         3882    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002c2   0x080002c2   0x00000000   Code   RO         3872    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002c2   0x080002c2   0x00000004   Code   RO         3873    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000000   Code   RO         3875    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000008   Code   RO         3876    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002ce   0x080002ce   0x00000002   Code   RO         4001    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002d0   0x080002d0   0x00000000   Code   RO         4033    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002d0   0x080002d0   0x00000004   Code   RO         4034    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002d4   0x080002d4   0x00000006   Code   RO         4035    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002da   0x080002da   0x00000002   PAD
    0x080002dc   0x080002dc   0x00000000   Code   RO         3895    .emb_text           c_w.l(maybetermalloc1.o)
    0x080002dc   0x080002dc   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x0800031c   0x0800031c   0x0000005e   Code   RO         3524    .text               c_w.l(h1_alloc.o)
    0x0800037a   0x0800037a   0x0000004e   Code   RO         3526    .text               c_w.l(h1_free.o)
    0x080003c8   0x080003c8   0x000000ee   Code   RO         3582    .text               c_w.l(lludivv7m.o)
    0x080004b6   0x080004b6   0x00000002   PAD
    0x080004b8   0x080004b8   0x00000034   Code   RO         3584    .text               c_w.l(vsnprintf.o)
    0x080004ec   0x080004ec   0x0000004e   Code   RO         3590    .text               c_w.l(rt_memclr_w.o)
    0x0800053a   0x0800053a   0x00000006   Code   RO         3592    .text               c_w.l(heapauxi.o)
    0x08000540   0x08000540   0x0000000c   Code   RO         3648    .text               c_w.l(sys_exit.o)
    0x0800054c   0x0800054c   0x00000008   Code   RO         3660    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x08000554   0x08000554   0x00000004   Code   RO         3662    .text               c_w.l(hguard.o)
    0x08000558   0x08000558   0x0000008a   Code   RO         3664    .text               c_w.l(init_alloc.o)
    0x080005e2   0x080005e2   0x0000000e   Code   RO         3668    .text               c_w.l(h1_init.o)
    0x080005f0   0x080005f0   0x00000016   Code   RO         3682    .text               c_w.l(_rserrno.o)
    0x08000606   0x08000606   0x0000004e   Code   RO         3686    .text               c_w.l(_printf_pad.o)
    0x08000654   0x08000654   0x00000024   Code   RO         3688    .text               c_w.l(_printf_truncate.o)
    0x08000678   0x08000678   0x00000052   Code   RO         3690    .text               c_w.l(_printf_str.o)
    0x080006ca   0x080006ca   0x00000002   PAD
    0x080006cc   0x080006cc   0x00000078   Code   RO         3692    .text               c_w.l(_printf_dec.o)
    0x08000744   0x08000744   0x00000028   Code   RO         3694    .text               c_w.l(_printf_charcount.o)
    0x0800076c   0x0800076c   0x00000030   Code   RO         3696    .text               c_w.l(_printf_char_common.o)
    0x0800079c   0x0800079c   0x0000000a   Code   RO         3698    .text               c_w.l(_sputc.o)
    0x080007a6   0x080007a6   0x00000010   Code   RO         3700    .text               c_w.l(_snputc.o)
    0x080007b6   0x080007b6   0x00000002   PAD
    0x080007b8   0x080007b8   0x000000bc   Code   RO         3702    .text               c_w.l(_printf_wctomb.o)
    0x08000874   0x08000874   0x0000007c   Code   RO         3705    .text               c_w.l(_printf_longlong_dec.o)
    0x080008f0   0x080008f0   0x00000070   Code   RO         3711    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000960   0x08000960   0x00000094   Code   RO         3731    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x080009f4   0x080009f4   0x00000188   Code   RO         3751    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000b7c   0x08000b7c   0x00000008   Code   RO         3863    .text               c_w.l(libspace.o)
    0x08000b84   0x08000b84   0x00000002   Code   RO         3866    .text               c_w.l(use_no_semi.o)
    0x08000b86   0x08000b86   0x00000000   Code   RO         3868    .text               c_w.l(indicate_semi.o)
    0x08000b86   0x08000b86   0x00000002   PAD
    0x08000b88   0x08000b88   0x00000008   Code   RO         3891    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000b90   0x08000b90   0x00000034   Code   RO         3897    .text               c_w.l(h1_extend.o)
    0x08000bc4   0x08000bc4   0x0000008a   Code   RO         3901    .text               c_w.l(lludiv10.o)
    0x08000c4e   0x08000c4e   0x000000b2   Code   RO         3903    .text               c_w.l(_printf_intcommon.o)
    0x08000d00   0x08000d00   0x0000041e   Code   RO         3905    .text               c_w.l(_printf_fp_dec.o)
    0x0800111e   0x0800111e   0x00000002   PAD
    0x08001120   0x08001120   0x000002fc   Code   RO         3907    .text               c_w.l(_printf_fp_hex.o)
    0x0800141c   0x0800141c   0x0000002c   Code   RO         3912    .text               c_w.l(_printf_char.o)
    0x08001448   0x08001448   0x0000002c   Code   RO         3914    .text               c_w.l(_printf_wchar.o)
    0x08001474   0x08001474   0x00000040   Code   RO         3917    .text               c_w.l(_wcrtomb.o)
    0x080014b4   0x080014b4   0x0000000a   Code   RO         3919    .text               c_w.l(defsig_exit.o)
    0x080014be   0x080014be   0x0000000e   Code   RO         3923    .text               c_w.l(defsig_rtmem_outer.o)
    0x080014cc   0x080014cc   0x0000004a   Code   RO         3934    .text               c_w.l(sys_stackheap_outer.o)
    0x08001516   0x08001516   0x00000002   PAD
    0x08001518   0x08001518   0x00000010   Code   RO         3936    .text               c_w.l(rt_ctype_table.o)
    0x08001528   0x08001528   0x00000008   Code   RO         3941    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001530   0x08001530   0x00000080   Code   RO         3945    .text               c_w.l(_printf_fp_infnan.o)
    0x080015b0   0x080015b0   0x000000e4   Code   RO         3947    .text               c_w.l(bigflt0.o)
    0x08001694   0x08001694   0x00000012   Code   RO         3975    .text               c_w.l(exit.o)
    0x080016a6   0x080016a6   0x00000032   Code   RO         3979    .text               c_w.l(defsig_general.o)
    0x080016d8   0x080016d8   0x00000050   Code   RO         3985    .text               c_w.l(defsig_rtmem_inner.o)
    0x08001728   0x08001728   0x0000000e   Code   RO         3999    .text               c_w.l(sys_wrch.o)
    0x08001736   0x08001736   0x00000002   PAD
    0x08001738   0x08001738   0x00000080   Code   RO         4014    .text               c_w.l(strcmpv7m.o)
    0x080017b8   0x080017b8   0x0000003e   Code   RO         3950    CL$$btod_d2e        c_w.l(btod.o)
    0x080017f6   0x080017f6   0x00000046   Code   RO         3952    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800183c   0x0800183c   0x00000060   Code   RO         3951    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800189c   0x0800189c   0x00000338   Code   RO         3960    CL$$btod_div_common  c_w.l(btod.o)
    0x08001bd4   0x08001bd4   0x000000dc   Code   RO         3957    CL$$btod_e2e        c_w.l(btod.o)
    0x08001cb0   0x08001cb0   0x0000002a   Code   RO         3954    CL$$btod_ediv       c_w.l(btod.o)
    0x08001cda   0x08001cda   0x0000002a   Code   RO         3953    CL$$btod_emul       c_w.l(btod.o)
    0x08001d04   0x08001d04   0x00000244   Code   RO         3959    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001f48   0x08001f48   0x00000074   Code   RO         3125    i.BNO080_HardwareReset  bno08x_hal.o
    0x08001fbc   0x08001fbc   0x0000000c   Code   RO         3126    i.BNO080_Init       bno08x_hal.o
    0x08001fc8   0x08001fc8   0x00000002   Code   RO          367    i.BusFault_Handler  stm32f4xx_it.o
    0x08001fca   0x08001fca   0x00000002   PAD
    0x08001fcc   0x08001fcc   0x0000000c   Code   RO          368    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x08001fd8   0x08001fd8   0x0000000c   Code   RO          369    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x08001fe4   0x08001fe4   0x00000028   Code   RO         1411    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x0800200c   0x0800200c   0x00000054   Code   RO         1412    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002060   0x08002060   0x00000002   Code   RO          370    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002062   0x08002062   0x00000006   Code   RO          371    i.EXTI3_IRQHandler  stm32f4xx_it.o
    0x08002068   0x08002068   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x0800206c   0x0800206c   0x00000092   Code   RO         1414    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080020fe   0x080020fe   0x00000024   Code   RO         1415    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002122   0x08002122   0x00000002   PAD
    0x08002124   0x08002124   0x000001a0   Code   RO         1419    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x080022c4   0x080022c4   0x000000d4   Code   RO         1420    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002398   0x08002398   0x00000024   Code   RO         1851    i.HAL_Delay         stm32f4xx_hal.o
    0x080023bc   0x080023bc   0x00000002   Code   RO         1305    i.HAL_GPIO_EXTI_Callback  stm32f4xx_hal_gpio.o
    0x080023be   0x080023be   0x00000002   PAD
    0x080023c0   0x080023c0   0x00000018   Code   RO         1306    i.HAL_GPIO_EXTI_IRQHandler  stm32f4xx_hal_gpio.o
    0x080023d8   0x080023d8   0x000001f0   Code   RO         1307    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080025c8   0x080025c8   0x0000000a   Code   RO         1309    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x080025d2   0x080025d2   0x0000000a   Code   RO         1311    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x080025dc   0x080025dc   0x0000000c   Code   RO         1857    i.HAL_GetTick       stm32f4xx_hal.o
    0x080025e8   0x080025e8   0x00000188   Code   RO          508    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x08002770   0x08002770   0x000001f0   Code   RO          514    i.HAL_I2C_Master_Receive  stm32f4xx_hal_i2c.o
    0x08002960   0x08002960   0x0000012c   Code   RO          521    i.HAL_I2C_Master_Transmit  stm32f4xx_hal_i2c.o
    0x08002a8c   0x08002a8c   0x00000130   Code   RO          529    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08002bbc   0x08002bbc   0x000000ac   Code   RO          272    i.HAL_I2C_MspInit   i2c.o
    0x08002c68   0x08002c68   0x00000010   Code   RO         1863    i.HAL_IncTick       stm32f4xx_hal.o
    0x08002c78   0x08002c78   0x00000034   Code   RO         1864    i.HAL_Init          stm32f4xx_hal.o
    0x08002cac   0x08002cac   0x00000040   Code   RO         1865    i.HAL_InitTick      stm32f4xx_hal.o
    0x08002cec   0x08002cec   0x00000030   Code   RO          473    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002d1c   0x08002d1c   0x0000001a   Code   RO         1699    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002d36   0x08002d36   0x00000002   PAD
    0x08002d38   0x08002d38   0x00000040   Code   RO         1705    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002d78   0x08002d78   0x00000024   Code   RO         1706    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002d9c   0x08002d9c   0x00000134   Code   RO          953    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08002ed0   0x08002ed0   0x00000020   Code   RO          960    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08002ef0   0x08002ef0   0x00000020   Code   RO          961    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08002f10   0x08002f10   0x00000060   Code   RO          962    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08002f70   0x08002f70   0x0000036c   Code   RO          965    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080032dc   0x080032dc   0x00000028   Code   RO         1710    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08003304   0x08003304   0x00000002   Code   RO         2114    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x08003306   0x08003306   0x00000002   Code   RO         2128    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x08003308   0x08003308   0x00000280   Code   RO         2131    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x08003588   0x08003588   0x00000064   Code   RO         2132    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x080035ec   0x080035ec   0x00000164   Code   RO          320    i.HAL_UART_MspInit  usart.o
    0x08003750   0x08003750   0x00000002   Code   RO         2138    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08003752   0x08003752   0x00000002   Code   RO         2143    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003754   0x08003754   0x00000002   Code   RO          372    i.HardFault_Handler  stm32f4xx_it.o
    0x08003756   0x08003756   0x0000002e   Code   RO          551    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08003784   0x08003784   0x000000ec   Code   RO          554    i.I2C_MasterRequestRead  stm32f4xx_hal_i2c.o
    0x08003870   0x08003870   0x0000009c   Code   RO          555    i.I2C_MasterRequestWrite  stm32f4xx_hal_i2c.o
    0x0800390c   0x0800390c   0x000000a8   Code   RO          562    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x080039b4   0x080039b4   0x00000056   Code   RO          566    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003a0a   0x08003a0a   0x00000002   PAD
    0x08003a0c   0x08003a0c   0x00000090   Code   RO          567    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003a9c   0x08003a9c   0x000000bc   Code   RO          568    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003b58   0x08003b58   0x00000070   Code   RO          569    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003bc8   0x08003bc8   0x00000056   Code   RO          570    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08003c1e   0x08003c1e   0x00000002   PAD
    0x08003c20   0x08003c20   0x0000004c   Code   RO          247    i.MX_DMA_Init       dma.o
    0x08003c6c   0x08003c6c   0x0000016c   Code   RO          223    i.MX_GPIO_Init      gpio.o
    0x08003dd8   0x08003dd8   0x00000040   Code   RO          273    i.MX_I2C1_Init      i2c.o
    0x08003e18   0x08003e18   0x00000040   Code   RO          274    i.MX_I2C2_Init      i2c.o
    0x08003e58   0x08003e58   0x00000038   Code   RO          321    i.MX_UART5_Init     usart.o
    0x08003e90   0x08003e90   0x00000038   Code   RO          322    i.MX_USART1_UART_Init  usart.o
    0x08003ec8   0x08003ec8   0x00000002   Code   RO          373    i.MemManage_Handler  stm32f4xx_it.o
    0x08003eca   0x08003eca   0x00000002   Code   RO          374    i.NMI_Handler       stm32f4xx_it.o
    0x08003ecc   0x08003ecc   0x00000034   Code   RO         2980    i.OLED_Clear        oled.o
    0x08003f00   0x08003f00   0x00000030   Code   RO         2983    i.OLED_Init         oled.o
    0x08003f30   0x08003f30   0x00000022   Code   RO         2984    i.OLED_Set_Position  oled.o
    0x08003f52   0x08003f52   0x00000002   PAD
    0x08003f54   0x08003f54   0x00000088   Code   RO         2985    i.OLED_ShowChar     oled.o
    0x08003fdc   0x08003fdc   0x00000036   Code   RO         2991    i.OLED_ShowStr      oled.o
    0x08004012   0x08004012   0x00000002   PAD
    0x08004014   0x08004014   0x00000024   Code   RO         2992    i.OLED_Write_cmd    oled.o
    0x08004038   0x08004038   0x00000024   Code   RO         2993    i.OLED_Write_data   oled.o
    0x0800405c   0x0800405c   0x00000002   Code   RO          375    i.PendSV_Handler    stm32f4xx_it.o
    0x0800405e   0x0800405e   0x00000002   PAD
    0x08004060   0x08004060   0x00000108   Code   RO         3127    i.QuaternionToEulerAngles  bno08x_hal.o
    0x08004168   0x08004168   0x00000002   Code   RO          376    i.SVC_Handler       stm32f4xx_it.o
    0x0800416a   0x0800416a   0x00000004   Code   RO          377    i.SysTick_Handler   stm32f4xx_it.o
    0x0800416e   0x0800416e   0x00000002   PAD
    0x08004170   0x08004170   0x00000094   Code   RO           14    i.SystemClock_Config  main.o
    0x08004204   0x08004204   0x00000010   Code   RO         2466    i.SystemInit        system_stm32f4xx.o
    0x08004214   0x08004214   0x0000000c   Code   RO          378    i.UART5_IRQHandler  stm32f4xx_it.o
    0x08004220   0x08004220   0x0000000e   Code   RO         2145    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800422e   0x0800422e   0x0000004e   Code   RO         2155    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x0800427c   0x0800427c   0x000000c2   Code   RO         2157    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x0800433e   0x0800433e   0x00000002   PAD
    0x08004340   0x08004340   0x0000010c   Code   RO         2158    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x0800444c   0x0800444c   0x0000000c   Code   RO          379    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08004458   0x08004458   0x00000002   Code   RO          380    i.UsageFault_Handler  stm32f4xx_it.o
    0x0800445a   0x0800445a   0x00000030   Code   RO         3997    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x0800448a   0x0800448a   0x00000026   Code   RO         3845    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x080044b0   0x080044b0   0x00000012   Code   RO         2635    i.__ARM_isinff      bno08x_app.o
    0x080044c2   0x080044c2   0x0000000c   Code   RO         2582    i.__ARM_isnanf      oled_app.o
    0x080044ce   0x080044ce   0x0000000c   Code   RO         2636    i.__ARM_isnanf      bno08x_app.o
    0x080044da   0x080044da   0x00000020   Code   RO         1712    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080044fa   0x080044fa   0x00000002   PAD
    0x080044fc   0x080044fc   0x0000012c   Code   RO         3598    i.__hardfp_asinf    m_wm.l(asinf.o)
    0x08004628   0x08004628   0x00000250   Code   RO         3610    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x08004878   0x08004878   0x00000664   Code   RO         3622    i.__hardfp_powf     m_wm.l(powf.o)
    0x08004edc   0x08004edc   0x0000003a   Code   RO         3636    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08004f16   0x08004f16   0x00000002   PAD
    0x08004f18   0x08004f18   0x00000014   Code   RO         3847    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08004f2c   0x08004f2c   0x00000006   Code   RO         3848    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x08004f32   0x08004f32   0x00000006   Code   RO         3849    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x08004f38   0x08004f38   0x00000010   Code   RO         3850    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x08004f48   0x08004f48   0x00000010   Code   RO         3851    i.__mathlib_flt_overflow  m_wm.l(funder.o)
    0x08004f58   0x08004f58   0x00000010   Code   RO         3853    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x08004f68   0x08004f68   0x0000000e   Code   RO         3744    i._is_digit         c_w.l(__printf_wp.o)
    0x08004f76   0x08004f76   0x00000002   PAD
    0x08004f78   0x08004f78   0x00000030   Code   RO         3092    i.all_task_init     my_scheduler.o
    0x08004fa8   0x08004fa8   0x0000003c   Code   RO         3093    i.all_task_run      my_scheduler.o
    0x08004fe4   0x08004fe4   0x000000d8   Code   RO         2637    i.bno080_task       bno08x_app.o
    0x080050bc   0x080050bc   0x00000064   Code   RO         2638    i.convert_to_continuous_yaw  bno08x_app.o
    0x08005120   0x08005120   0x00000030   Code   RO         3133    i.dataAvailable     bno08x_hal.o
    0x08005150   0x08005150   0x0000000a   Code   RO         3139    i.enableRotationVector  bno08x_hal.o
    0x0800515a   0x0800515a   0x00000002   PAD
    0x0800515c   0x0800515c   0x00000014   Code   RO         3165    i.getQuatI          bno08x_hal.o
    0x08005170   0x08005170   0x00000014   Code   RO         3166    i.getQuatJ          bno08x_hal.o
    0x08005184   0x08005184   0x00000014   Code   RO         3167    i.getQuatK          bno08x_hal.o
    0x08005198   0x08005198   0x00000014   Code   RO         3169    i.getQuatReal       bno08x_hal.o
    0x080051ac   0x080051ac   0x0000001c   Code   RO         2639    i.get_pitch         bno08x_app.o
    0x080051c8   0x080051c8   0x0000001c   Code   RO         2640    i.get_roll          bno08x_app.o
    0x080051e4   0x080051e4   0x0000001c   Code   RO         2641    i.get_yaw           bno08x_app.o
    0x08005200   0x08005200   0x00000050   Code   RO         2955    i.key_read          key_driver.o
    0x08005250   0x08005250   0x0000004c   Code   RO         2503    i.key_task          key_app.o
    0x0800529c   0x0800529c   0x00000080   Code   RO         2930    i.led_disp          led_driver.o
    0x0800531c   0x0800531c   0x00000002   Code   RO         2545    i.led_init          led_app.o
    0x0800531e   0x0800531e   0x00000002   PAD
    0x08005320   0x08005320   0x0000000c   Code   RO         2546    i.led_task          led_app.o
    0x0800532c   0x0800532c   0x0000002a   Code   RO           15    i.main              main.o
    0x08005356   0x08005356   0x00000002   PAD
    0x08005358   0x08005358   0x0000004c   Code   RO         2642    i.my_bno080_init    bno08x_app.o
    0x080053a4   0x080053a4   0x00000004   Code   RO         2583    i.my_oled_init      oled_app.o
    0x080053a8   0x080053a8   0x00000050   Code   RO         2584    i.oled_printf       oled_app.o
    0x080053f8   0x080053f8   0x000001e0   Code   RO         2585    i.oled_task         oled_app.o
    0x080055d8   0x080055d8   0x000000d8   Code   RO         3174    i.parseInputReport  bno08x_hal.o
    0x080056b0   0x080056b0   0x0000002a   Code   RO         3175    i.qToFloat          bno08x_hal.o
    0x080056da   0x080056da   0x00000002   PAD
    0x080056dc   0x080056dc   0x00000098   Code   RO         3178    i.receivePacket     bno08x_hal.o
    0x08005774   0x08005774   0x00000070   Code   RO         3183    i.sendPacket        bno08x_hal.o
    0x080057e4   0x080057e4   0x0000004c   Code   RO         3184    i.setFeatureCommand  bno08x_hal.o
    0x08005830   0x08005830   0x00000034   Code   RO         3185    i.softReset         bno08x_hal.o
    0x08005864   0x08005864   0x0000003e   Code   RO         3638    i.sqrtf             m_wm.l(sqrtf.o)
    0x080058a2   0x080058a2   0x00000002   PAD
    0x080058a4   0x080058a4   0x0000002c   Code   RO         3973    locale$$code        c_w.l(lc_numeric_c.o)
    0x080058d0   0x080058d0   0x0000002c   Code   RO         4008    locale$$code        c_w.l(lc_ctype_c.o)
    0x080058fc   0x080058fc   0x0000000c   Code   RO         3830    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08005908   0x08005908   0x00000056   Code   RO         3596    x$fpl$f2d           fz_wm.l(f2d.o)
    0x0800595e   0x0800595e   0x0000008c   Code   RO         3832    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x080059ea   0x080059ea   0x0000000a   Code   RO         3932    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x080059f4   0x080059f4   0x00000004   Code   RO         3836    x$fpl$printf1       fz_wm.l(printf1.o)
    0x080059f8   0x080059f8   0x00000004   Code   RO         3838    x$fpl$printf2       fz_wm.l(printf2.o)
    0x080059fc   0x080059fc   0x00000000   Code   RO         3844    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x080059fc   0x080059fc   0x00000008   Data   RO         1426    .constdata          stm32f4xx_hal_dma.o
    0x08005a04   0x08005a04   0x00000010   Data   RO         2467    .constdata          system_stm32f4xx.o
    0x08005a14   0x08005a14   0x00000008   Data   RO         2468    .constdata          system_stm32f4xx.o
    0x08005a1c   0x08005a1c   0x00000a98   Data   RO         2994    .constdata          oled.o
    0x080064b4   0x080064b4   0x00000140   Data   RO         3625    .constdata          m_wm.l(powf.o)
    0x080065f4   0x080065f4   0x00000008   Data   RO         3703    .constdata          c_w.l(_printf_wctomb.o)
    0x080065fc   0x080065fc   0x00000028   Data   RO         3732    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08006624   0x08006624   0x00000011   Data   RO         3752    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08006635   0x08006635   0x00000026   Data   RO         3908    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800665b   0x0800665b   0x00000001   PAD
    0x0800665c   0x0800665c   0x00000094   Data   RO         3948    .constdata          c_w.l(bigflt0.o)
    0x080066f0   0x080066f0   0x00000020   Data   RO         4054    Region$$Table       anon$$obj.o
    0x08006710   0x08006710   0x0000001c   Data   RO         3972    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800672c   0x0800672c   0x00000110   Data   RO         4007    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800683c, Size: 0x000009f0, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800683c   0x0000000c   Data   RW         1871    .data               stm32f4xx_hal.o
    0x2000000c   0x08006848   0x00000004   Data   RW         2469    .data               system_stm32f4xx.o
    0x20000010   0x0800684c   0x00000005   Data   RW         2504    .data               key_app.o
    0x20000015   0x08006851   0x00000005   Data   RW         2547    .data               led_app.o
    0x2000001a   0x08006856   0x00000002   PAD
    0x2000001c   0x08006858   0x00000008   Data   RW         2586    .data               oled_app.o
    0x20000024   0x08006860   0x0000001c   Data   RW         2643    .data               bno08x_app.o
    0x20000040   0x0800687c   0x00000001   Data   RW         2931    .data               led_driver.o
    0x20000041   0x0800687d   0x00000016   Data   RW         2995    .data               oled.o
    0x20000057   0x08006893   0x00000001   PAD
    0x20000058   0x08006894   0x00000034   Data   RW         3094    .data               my_scheduler.o
    0x2000008c   0x080068c8   0x00000056   Data   RW         3188    .data               bno08x_hal.o
    0x200000e2   0x0800691e   0x00000002   PAD
    0x200000e4        -       0x000000a8   Zero   RW          275    .bss                i2c.o
    0x2000018c        -       0x00000150   Zero   RW          323    .bss                usart.o
    0x200002dc        -       0x000000a4   Zero   RW         3186    .bss                bno08x_hal.o
    0x20000380        -       0x0000000a   Zero   RW         3187    .bss                bno08x_hal.o
    0x2000038a   0x0800691e   0x00000002   PAD
    0x2000038c        -       0x00000060   Zero   RW         3864    .bss                c_w.l(libspace.o)
    0x200003ec   0x0800691e   0x00000004   PAD
    0x200003f0        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x200005f0        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08006920, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       506         56          0         28          0       9760   bno08x_app.o
      1180         98          0         86        174      12000   bno08x_hal.o
        76          4          0          0          0        798   dma.o
       364         20          0          0          0       1111   gpio.o
       300         46          0          0        168       2318   i2c.o
        76          8          0          5          0        855   key_app.o
        80         10          0          0          0        571   key_driver.o
        14          6          0          5          0       1332   led_app.o
       128         12          0          1          0        899   led_driver.o
       194         10          0          0          0     667508   main.o
       108         10          0         52          0       1715   my_scheduler.o
       396         28       2712         22          0       5387   oled.o
       576        180          0          8          0       7796   oled_app.o
        64         26        392          0       1536        832   startup_stm32f407xx.o
       180         28          0         12          0       9325   stm32f4xx_hal.o
       198         14          0          0          0      33863   stm32f4xx_hal_cortex.o
       934         16          8          0          0       5547   stm32f4xx_hal_dma.o
       542         52          0          0          0       3971   stm32f4xx_hal_gpio.o
      2714         68          0          0          0      14994   stm32f4xx_hal_i2c.o
        48          6          0          0          0        838   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5200   stm32f4xx_hal_rcc.o
      1302         14          0          0          0       8236   stm32f4xx_hal_uart.o
        74         24          0          0          0       6388   stm32f4xx_it.o
        16          4         24          4          0       1099   system_stm32f4xx.o
       468         52          0          0        336       2590   usart.o

    ----------------------------------------------------------------------
     11914        <USER>       <GROUP>        228       2216     804933   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        32          0          0          5          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        18          0          0          0          0         80   exit.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        42          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
         0          0          0          0          0          0   maybetermalloc1.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       300         42          0          0          0        176   asinf.o
       592         90          0          0          0        184   atan2f.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        80         24          0          0          0        696   funder.o
      1636        110        320          0          0        372   powf.o
       120          0          0          0          0        272   sqrtf.o

    ----------------------------------------------------------------------
     10730        <USER>        <GROUP>          0        100       8052   Library Totals
        24          0          1          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7636        332        551          0         96       5384   c_w.l
       256          8          0          0          0        728   fz_wm.l
      2814        266        320          0          0       1940   m_wm.l

    ----------------------------------------------------------------------
     10730        <USER>        <GROUP>          0        100       8052   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     22644       1470       4040        228       2316     797469   Grand Totals
     22644       1470       4040        228       2316     797469   ELF Image Totals
     22644       1470       4040        228          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                26684 (  26.06kB)
    Total RW  Size (RW Data + ZI Data)              2544 (   2.48kB)
    Total ROM Size (Code + RO Data + RW Data)      26912 (  26.28kB)

==============================================================================

