#include "debug_helper.h"

/**
 * @brief 测试OLED显示功能
 * @note 用于诊断OLED是否正常工作
 */
void test_oled_display(void)
{
    static uint8_t test_step = 0;
    
    switch(test_step) {
        case 0:
            oled_printf(0, 0, "OLED Test 1");
            oled_printf(0, 1, "Basic Text");
            break;
        case 1:
            oled_printf(0, 0, "OLED Test 2");
            oled_printf(0, 1, "Numbers: %d", 12345);
            break;
        case 2:
            oled_printf(0, 0, "OLED Test 3");
            oled_printf(0, 1, "Float: %.2f", 123.45f);
            break;
        case 3:
            oled_printf(0, 0, "OLED Test 4");
            oled_printf(0, 1, "Count: %d", count);
            break;
        default:
            test_step = 0;
            return;
    }
    
    test_step++;
    if (test_step > 3) test_step = 0;
}

/**
 * @brief 测试BNO08X传感器功能
 * @note 用于诊断BNO08X是否正常工作
 */
void test_bno08x_sensor(void)
{
    // 检查数据是否可用
    if (dataAvailable()) {
        oled_printf(0, 0, "BNO08X: OK");
        oled_printf(0, 1, "Data Ready");
    } else {
        oled_printf(0, 0, "BNO08X: ERR");
        oled_printf(0, 1, "No Data");
    }
}

/**
 * @brief 检查I2C总线状态
 * @note 用于诊断I2C总线是否有冲突
 */
void check_i2c_status(void)
{
    // 检查I2C1状态（BNO08X）
    if (hi2c1.State == HAL_I2C_STATE_READY) {
        oled_printf(0, 0, "I2C1: Ready");
    } else {
        oled_printf(0, 0, "I2C1: Busy");
    }
    
    // 检查I2C2状态（如果OLED使用I2C2）
    #ifdef hi2c2
    if (hi2c2.State == HAL_I2C_STATE_READY) {
        oled_printf(0, 1, "I2C2: Ready");
    } else {
        oled_printf(0, 1, "I2C2: Busy");
    }
    #endif
}

/**
 * @brief 安全的OLED显示任务
 * @note 带有错误处理的OLED显示
 */
void safe_oled_task(void)
{
    static uint8_t error_count = 0;
    static uint32_t last_update = 0;
    uint32_t current_time = HAL_GetTick();
    
    // 限制更新频率，避免过于频繁的I2C操作
    if (current_time - last_update < 500) {  // 500ms更新一次
        return;
    }
    last_update = current_time;
    
    // 尝试获取BNO08X数据
    float roll = get_roll();
    float pitch = get_pitch();
    float yaw = get_yaw();
    
    // 检查数据有效性
    if (isnan(roll) || isnan(pitch) || isnan(yaw)) {
        error_count++;
        oled_printf(0, 0, "BNO ERR:%d", error_count);
        oled_printf(0, 1, "Check Conn");
        oled_printf(0, 2, "cnt:%d", count);
        return;
    }
    
    // 重置错误计数
    error_count = 0;
    
    // 显示姿态角数据
    oled_printf(0, 0, "R:%.1f", roll);
    oled_printf(64, 0, "P:%.1f", pitch);
    oled_printf(0, 1, "Yaw:%.1f", yaw);
    oled_printf(0, 2, "cnt:%d", count);
}
