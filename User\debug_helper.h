#ifndef __DEBUG_HELPER_H_
#define __DEBUG_HELPER_H_

#include "mydefine.h"

/**
 * @brief 测试OLED显示功能
 * @note 用于诊断OLED是否正常工作
 */
void test_oled_display(void);

/**
 * @brief 测试BNO08X传感器功能
 * @note 用于诊断BNO08X是否正常工作
 */
void test_bno08x_sensor(void);

/**
 * @brief 检查I2C总线状态
 * @note 用于诊断I2C总线是否有冲突
 */
void check_i2c_status(void);

/**
 * @brief 安全的OLED显示任务
 * @note 带有错误处理的OLED显示
 */
void safe_oled_task(void);

#endif /* __DEBUG_HELPER_H_ */
