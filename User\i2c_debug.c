#include "i2c_debug.h"

/**
 * @brief 检查I2C总线状态
 * @param hi2c I2C句柄指针
 * @return 0:正常, -1:异常
 */
int check_i2c_bus_status(I2C_HandleTypeDef *hi2c)
{
    if (hi2c == NULL) {
        return -1;
    }
    
    // 检查I2C状态
    if (hi2c->State != HAL_I2C_STATE_READY) {
        return -1;
    }
    
    return 0;
}

/**
 * @brief 扫描I2C总线上的设备
 * @param hi2c I2C句柄指针
 * @param devices 存储找到的设备地址数组
 * @param max_devices 最大设备数量
 * @return 找到的设备数量
 */
int scan_i2c_devices(I2C_HandleTypeDef *hi2c, uint8_t *devices, uint8_t max_devices)
{
    uint8_t device_count = 0;
    
    if (hi2c == NULL || devices == NULL) {
        return 0;
    }
    
    // 扫描I2C地址范围 0x08 到 0x77
    for (uint8_t addr = 0x08; addr <= 0x77; addr++) {
        // 尝试与设备通信
        if (HAL_I2C_IsDeviceReady(hi2c, addr << 1, 1, 10) == HAL_OK) {
            if (device_count < max_devices) {
                devices[device_count] = addr;
                device_count++;
            }
        }
    }
    
    return device_count;
}

/**
 * @brief 测试OLED通信
 * @return 0:成功, -1:失败
 */
int test_oled_communication(void)
{
    // OLED地址通常是0x3C (0x78 >> 1)
    if (HAL_I2C_IsDeviceReady(&hi2c2, 0x78, 3, 100) == HAL_OK) {
        return 0;  // OLED通信正常
    }
    return -1;  // OLED通信失败
}

/**
 * @brief 测试BNO08X通信
 * @return 0:成功, -1:失败
 */
int test_bno08x_communication(void)
{
    // BNO08X地址通常是0x4B (0x96 >> 1)
    if (HAL_I2C_IsDeviceReady(&hi2c1, 0x4B << 1, 3, 100) == HAL_OK) {
        return 0;  // BNO08X通信正常
    }
    return -1;  // BNO08X通信失败
}
