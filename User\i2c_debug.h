#ifndef __I2C_DEBUG_H_
#define __I2C_DEBUG_H_

#include "mydefine.h"

/**
 * @brief 检查I2C总线状态
 * @param hi2c I2C句柄指针
 * @return 0:正常, -1:异常
 */
int check_i2c_bus_status(I2C_HandleTypeDef *hi2c);

/**
 * @brief 扫描I2C总线上的设备
 * @param hi2c I2C句柄指针
 * @param devices 存储找到的设备地址数组
 * @param max_devices 最大设备数量
 * @return 找到的设备数量
 */
int scan_i2c_devices(I2C_HandleTypeDef *hi2c, uint8_t *devices, uint8_t max_devices);

/**
 * @brief 测试OLED通信
 * @return 0:成功, -1:失败
 */
int test_oled_communication(void);

/**
 * @brief 测试BNO08X通信
 * @return 0:成功, -1:失败
 */
int test_bno08x_communication(void);

#endif /* __I2C_DEBUG_H_ */
