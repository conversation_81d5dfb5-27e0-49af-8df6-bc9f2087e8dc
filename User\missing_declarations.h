#ifndef __MISSING_DECLARATIONS_H_
#define __MISSING_DECLARATIONS_H_

#include "mydefine.h"

/* 定时器句柄声明 - 需要在CubeMX中配置TIM2 */
/* 注释掉未配置的定时器，避免编译错误 */
// extern TIM_HandleTypeDef htim2;

/* 编码器相关声明 */
typedef struct {
    int speed_cm_s;
    // 添加其他需要的字段
} encoder_t;

extern encoder_t left_encoder;

/* 灰度传感器相关声明 */
extern uint8_t Digtal;  // 灰度传感器数字值

/* PID控制相关声明 */
typedef struct {
    float kp, ki, kd;
    float target;
    float error;
    float last_error;
    float integral;
    float output;
    // 添加其他需要的字段
} pid_t;

extern pid_t pid_line;   // 循线PID
extern pid_t pid_angle;  // 角度PID
extern uint8_t pid_control_mode;  // PID控制模式
extern uint8_t pid_running;       // PID运行标志
extern uint8_t stop_flat;         // 停止标志
extern int basic_speed;            // 基础速度

/* 函数声明 */
void Encoder_Task(void);
void Gray_Task(void);
void PID_Task(void);
void pid_set_target(pid_t *pid, float target);
void pid_reset(pid_t *pid);

#endif /* __MISSING_DECLARATIONS_H_ */
