#include "my_printf.h"
#include <stdarg.h>
#include <stdio.h>

/**
 * @brief 通过UART发送单个字符
 * @param huart UART句柄指针
 * @param ch 要发送的字符
 * @return 发送状态 (0:成功, -1:失败)
 */
int my_putchar(UART_HandleTypeDef *huart, char ch)
{
    if (huart == NULL) {
        return -1;
    }
    
    // 使用HAL库发送单个字符，超时时间1000ms
    if (HAL_UART_Transmit(huart, (uint8_t*)&ch, 1, 1000) == HAL_OK) {
        return 0;
    }
    return -1;
}

/**
 * @brief 通过UART发送字符串
 * @param huart UART句柄指针
 * @param str 要发送的字符串
 * @return 发送的字符数
 */
int my_puts(UART_HandleTypeDef *huart, const char *str)
{
    if (huart == NULL || str == NULL) {
        return -1;
    }
    
    int count = 0;
    while (*str) {
        if (my_putchar(huart, *str) == 0) {
            count++;
        } else {
            break;  // 发送失败则停止
        }
        str++;
    }
    return count;
}

/**
 * @brief 通过UART发送格式化字符串
 * @param huart UART句柄指针
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 发送的字符数
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    if (huart == NULL || format == NULL) {
        return -1;
    }
    
    char buffer[512];  // 缓冲区，可根据需要调整大小
    va_list args;
    int len;
    
    // 格式化字符串到缓冲区
    va_start(args, format);
    len = vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    
    // 检查格式化是否成功
    if (len < 0) {
        return -1;
    }
    
    // 确保不超出缓冲区
    if (len >= sizeof(buffer)) {
        len = sizeof(buffer) - 1;
        buffer[len] = '\0';
    }
    
    // 发送格式化后的字符串
    if (HAL_UART_Transmit(huart, (uint8_t*)buffer, len, 1000) == HAL_OK) {
        return len;
    }
    
    return -1;
}
