#ifndef __MY_PRINTF_H_
#define __MY_PRINTF_H_

#include "mydefine.h"

/**
 * @brief 通过UART发送格式化字符串
 * @param huart UART句柄指针
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 发送的字符数
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

/**
 * @brief 通过UART发送字符串
 * @param huart UART句柄指针
 * @param str 要发送的字符串
 * @return 发送的字符数
 */
int my_puts(UART_HandleTypeDef *huart, const char *str);

/**
 * @brief 通过UART发送单个字符
 * @param huart UART句柄指针
 * @param ch 要发送的字符
 * @return 发送状态 (0:成功, -1:失败)
 */
int my_putchar(UART_HandleTypeDef *huart, char ch);

#endif /* __MY_PRINTF_H_ */
