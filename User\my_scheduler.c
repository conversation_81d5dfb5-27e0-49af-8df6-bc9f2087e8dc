#include "my_scheduler.h"

typedef struct{
	void(*task_fun)(void);
	uint32_t task_time;
	uint32_t last_time;
}task;

task all_task[]={
	{led_task,1,0},
	{key_task,10,0},
	{oled_task,1000,0},     // OLED任务，1秒间隔，减少I2C负载
	{bno080_task,200,0},    // BNO08X任务，200ms间隔，错开执行

};

uint8_t task_num;

void all_task_init(void)
{
	task_num = sizeof(all_task)/sizeof(task);

	// 先初始化LED（不使用I2C）
	led_init();
	HAL_Delay(100);

	// 再初始化OLED（使用I2C2）
	my_oled_init();
	HAL_Delay(200);

	// 最后初始化BNO08X（使用I2C1）
	if (my_bno080_init() != 0) {
		// BNO08X初始化失败，在OLED上显示错误
		// 这里可以添加错误处理
	}
	HAL_Delay(200);
}

void all_task_run(void)
{
	for(uint8_t i=0;i<task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= all_task[i].last_time + all_task[i].task_time)
		{
			all_task[i].last_time = now_time;
			all_task[i].task_fun();
		}
	}	
}
