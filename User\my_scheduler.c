#include "my_scheduler.h"

typedef struct{
	void(*task_fun)(void);
	uint32_t task_time;
	uint32_t last_time;
}task;

task all_task[]={
	{led_task,1,0},
	{key_task,10,0},
	{safe_oled_task,500,0}, // 使用安全的OLED任务，500ms间隔
	{bno080_task,100,0},    // BNO08X读取间隔100ms

};

uint8_t task_num;

void all_task_init(void)
{
	task_num = sizeof(all_task)/sizeof(task);
	my_oled_init();
	led_init();    
	my_bno080_init();

}

void all_task_run(void)
{
	for(uint8_t i=0;i<task_num;i++)
	{
		uint32_t now_time = HAL_GetTick();
		if(now_time >= all_task[i].last_time + all_task[i].task_time)
		{
			all_task[i].last_time = now_time;
			all_task[i].task_fun();
		}
	}	
}
