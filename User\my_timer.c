#include "my_timer.h"

extern int basic_speed;

unsigned char measure_timer5ms;
unsigned char key_timer10ms;

unsigned char output_ff_flag;
unsigned int output_timer500ms = 0;

unsigned char intput_ff_flag;
unsigned int intput_timer500ms;

unsigned int led_timer500ms; // ÿ����һ���㣬LED ���� 500ms ��Ϩ��

unsigned char point_count = 0; // �����ĵ�λ��������Ȧ + 1����Ȧ + 1��

unsigned char system_mode = 1; // ϵͳ״̬��1 ~ 4 ��Ӧ 4 ����Ŀ��

unsigned char circle_count = 0; // �������Ȧ��������

unsigned int distance = 0; // ��¼С��ÿһ����ʻ�ľ���

extern uint8_t led_rgb[5];

void timer_init(void)
{
  // 定时器未配置，暂时注释掉
  // HAL_TIM_Base_Start_IT(&htim2);
}

// TIM2 中断服务函数（1ms 中断）
// 定时器未配置，暂时注释掉整个函数
/*
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  if(htim->Instance == htim2.Instance)
	{
		if(++measure_timer5ms >= 5)
		{
			measure_timer5ms = 0;

			Encoder_Task();
			distance += left_encoder.speed_cm_s;
			Gray_Task();
			bno080_task();
			PID_Task();
		}

//		if(pid_running != 1) return;

		// 圆环检测器
		if(Digtal != 0x00)
		{
			output_ff_flag = 1;
			if(++intput_timer500ms >= 500) intput_timer500ms = 500;
		}
		else if(output_ff_flag == 1 && intput_timer500ms == 500)
		{
			output_ff_flag = 0;
			intput_timer500ms = 0;
			point_count++;
			Car_State_Update();
		}
////
////	  // 圆环检测器
	  if(Digtal == 0x00)
	  {
	    intput_ff_flag = 1;
	    if(++output_timer500ms >= 500) output_timer500ms = 500;
	  }
	  else if(intput_ff_flag == 1 && output_timer500ms == 500)
	  {
	    intput_ff_flag = 0;
	    output_timer500ms = 0;
	    point_count++;
	    Car_State_Update();
	  }


		// LED 状态控制
	  if(led_rgb[0] == 1 && ++led_timer500ms >= 500)
	  {
	    led_rgb[0] = 0;
	    led_timer500ms = 0;
	  }
	}
}
*/

extern uint8_t stop_flat;
//ÿ�ε�λ��������ʱ������ϵͳ״̬ͬ������С����״̬
void Car_State_Update(void)
{
  led_rgb[0] = 1;
  distance = 0;
  
  switch(system_mode)
  {
    case 1: // ��һ�⣺ֱ����ʻ A -> B
      if(point_count == 1)
      {
//        pid_running = 0;
////        motor_break();
				point_count = 0;
				stop_flat = 1;
      }
      break;
    case 2: // �ڶ��⣺����һȦ A -> B -> C -> D
      if(point_count == 1)
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
				
//				pid_running = 0;
//        motor_break();
				
        pid_set_target(&pid_angle, 179);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 4)
      {
//        pid_running = 0;
//        motor_break();
				stop_flat = 1; 
      }
      break;
    case 3: // �����⣺8 �ֻ���һȦ A -> C -> B -> D
      if(point_count == 1)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
				
//				pid_running = 0;
//        motor_break();
				
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 4)
      {
//        pid_running = 0;
//        motor_break();
				stop_flat = 1;
      }
      break;
    case 4: // �����⣺8 �ֻ�����Ȧ
      if(point_count == 1)
      {
        pid_control_mode = 1; // ʹ��ѭ��������
      }
      else if(point_count == 2)
      {
        pid_control_mode = 0; // ʹ�ýǶȻ�����
        pid_set_target(&pid_angle, -215);
      }
      else if(point_count == 3)
        pid_control_mode = 1; // ʹ��ѭ��������
      else if(point_count == 4)
      {
				pid_set_target(&pid_angle, 36);
        if(++circle_count >= 4)
        {
//          pid_running = 0;
//          motor_break();
					stop_flat = 1;
        }
        point_count = 0;
        pid_control_mode = 0; // ʹ�ýǶȻ�����
      }
      break;
  }
  
  /* ������ʷ��� */
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}
