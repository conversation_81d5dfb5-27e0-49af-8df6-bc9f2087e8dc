#ifndef __MYDEFINE_H_
#define __MYDEFINE_H_

/*��׼C�⺯��*/
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/*HAL��*/
#include "main.h"

/*APP*/
#include "led_app.h"
#include "key_app.h"
#include "oled_app.h"
#include "my_timer.h"
#include "my_printf.h"
#include "debug_helper.h"
#include "i2c_debug.h"

/*调度器*/
#include "my_scheduler.h"
#include "bno08x_app.h"

extern uint8_t led_rgb[5];//ǰ3������rgb. ����������led1,led2,        led3��4��sd�����ýӿڣ�������
extern uint8_t count;

#endif
