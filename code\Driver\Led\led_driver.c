#include "led_driver.h"

void led_disp(uint8_t* led_buf)
{
	uint8_t temp = 0x00;
	static uint8_t temp_old = 0x00;
	temp = (led_buf[0]<<0)|(led_buf[1]<<1)|(led_buf[2]<<2)|(led_buf[3]<<3)|(led_buf[4]<<4);
	
	if(temp != temp_old)
	{
		HAL_GPIO_WritePin(LED1_R_GPIO_Port, LED1_R_Pin, (temp&(1<<0))?GPIO_PIN_RESET:GPIO_PIN_SET);
		HAL_GPIO_WritePin(LED1_G_GPIO_Port, LED1_G_Pin, (temp&(1<<1))?GPIO_PIN_RESET:GPIO_PIN_SET);
		HAL_GPIO_WritePin(LED1_B_GPIO_Port, LED1_B_Pin, (temp&(1<<2))?GPIO_PIN_RESET:GPIO_PIN_SET);
		HAL_GPIO_WritePin(LED1_GPIO_Port, LED1_Pin, (temp&(1<<3))?GPIO_PIN_RESET:GPIO_PIN_SET);
		HAL_GPIO_WritePin(LED2_GPIO_Port, LED2_Pin, (temp&(1<<4))?GPIO_PIN_RESET:GPIO_PIN_SET);
		temp_old = temp;
	}
}
