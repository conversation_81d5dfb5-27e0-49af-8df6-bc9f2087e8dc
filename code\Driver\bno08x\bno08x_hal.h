#ifndef __BNO08X_HAL_H_
#define __BNO08X_HAL_H_

#include "mydefine.h"  // STM32 HAL????????????????MCU??????????stm32f1xx_hal.h??

/** @defgroup BNO080_Constants BNO080????????
  * @{
  */

/** ???I2C?????BNO080???0x4A??0x4B????ADR??????? */
#define BNO080_DEFAULT_ADDRESS 0x4B

/** I2C?????????????????��???????? */
#define I2C_BUFFER_LENGTH 32

/** ??????????��??SHTP��?�E??????? */
#define MAX_PACKET_SIZE 128

/** FRS????????? */
#define MAX_METADATA_SIZE 9

/** SHTP??????? */
#define CHANNEL_COMMAND         0  // ???????
#define CHANNEL_EXECUTABLE      1  // ????????
#define CHANNEL_CONTROL         2  // ???????
#define CHANNEL_REPORTS         3  // ???????
#define CHANNEL_WAKE_REPORTS    4  // ??????????
#define CHANNEL_GYRO            5  // ?????????

/** SHTP????ID */
#define SHTP_REPORT_COMMAND_RESPONSE      0xF1  // ???????
#define SHTP_REPORT_COMMAND_REQUEST       0xF2  // ????????
#define SHTP_REPORT_FRS_READ_RESPONSE     0xF3  // FRS??????
#define SHTP_REPORT_FRS_READ_REQUEST      0xF4  // FRS???????
#define SHTP_REPORT_PRODUCT_ID_RESPONSE   0xF8  // ???ID???
#define SHTP_REPORT_PRODUCT_ID_REQUEST    0xF9  // ???ID????
#define SHTP_REPORT_BASE_TIMESTAMP        0xFB  // ????????
#define SHTP_REPORT_SET_FEATURE_COMMAND   0xFD  // ????????????

/** ??????????ID */
#define SENSOR_REPORTID_ACCELEROMETER            0x01  // ??????
#define SENSOR_REPORTID_GYROSCOPE                0x02  // ??????
#define SENSOR_REPORTID_MAGNETIC_FIELD           0x03  // ??????
#define SENSOR_REPORTID_LINEAR_ACCELERATION      0x04  // ????????
#define SENSOR_REPORTID_ROTATION_VECTOR          0x05  // ???????
#define SENSOR_REPORTID_GRAVITY                  0x06  // ????
#define SENSOR_REPORTID_GAME_ROTATION_VECTOR     0x08  // ??????????
#define SENSOR_REPORTID_GEOMAGNETIC_ROTATION_VECTOR 0x09  // ??????????
#define SENSOR_REPORTID_TAP_DETECTOR             0x10  // ??????
#define SENSOR_REPORTID_STEP_COUNTER             0x11  // ??????????
#define SENSOR_REPORTID_STABILITY_CLASSIFIER     0x13  // ??????????
#define SENSOR_REPORTID_PERSONAL_ACTIVITY_CLASSIFIER 0x1E  // ???????????

/** FRS???ID */
#define FRS_RECORDID_ACCELEROMETER          0xE302  // ????????
#define FRS_RECORDID_GYROSCOPE_CALIBRATED   0xE306  // ��?????????
#define FRS_RECORDID_MAGNETIC_FIELD_CALIBRATED 0xE309  // ��?????????
#define FRS_RECORDID_ROTATION_VECTOR        0xE30B  // ??????????

/** ????ID */
#define COMMAND_ERRORS         1   // ????????
#define COMMAND_COUNTER        2   // ??????????
#define COMMAND_TARE           3   // ��?????
#define COMMAND_INITIALIZE     4   // ?????????
#define COMMAND_DCD            6   // ?????????????????
#define COMMAND_ME_CALIBRATE   7   // ME��?????
#define COMMAND_DCD_PERIOD_SAVE 9  // ?????????DCD????
#define COMMAND_OSCILLATOR     10  // ????????
#define COMMAND_CLEAR_DCD      11  // ???DCD????

/** ��???? */
#define CALIBRATE_ACCEL          0  // ��???????
#define CALIBRATE_GYRO           1  // ��???????
#define CALIBRATE_MAG            2  // ��???????
#define CALIBRATE_PLANAR_ACCEL   3  // ��?????????
#define CALIBRATE_ACCEL_GYRO_MAG 4  // ��????????????????????
#define CALIBRATE_STOP           5  // ??��?

/**
  * @}
  */

/** @defgroup BNO080_Functions BNO080????????
  * @{
  */

/** ?????BNO080??????
  * @param hi2c I2C??????
  * @param address ?��I2C?????7��?????
  */
void BNO080_Init(I2C_HandleTypeDef *hi2c, uint8_t address);

/** Ӳ����λBNO08X������������nRESET���Ž���Ӳ����λ���ȴ�Product ID Response */
int8_t BNO080_HardwareReset(void);

/** ?????????�� */
void softReset(void);

/** ?????��???
  * @return ??��??????
  */
uint8_t resetReason(void);

/** ???????????????????
  * @param fixedPointValue ????????
  * @param qPoint Q??��??
  * @return ????????????
  */
float qToFloat(int16_t fixedPointValue, uint8_t qPoint);

/** ?????????????????
  * @return 1??????????0?????????
  */
uint8_t dataAvailable(void);

/** ?????????????? */
void parseInputReport(void);

/** ????????I???? */
float getQuatI(void);

/** ????????J???? */
float getQuatJ(void);

/** ????????K???? */
float getQuatK(void);

/** ??????????? */
float getQuatReal(void);

/** ??????????????? */
float getQuatRadianAccuracy(void);

/** ??????????????? */
uint8_t getQuatAccuracy(void);

/** ???X?????? */
float getAccelX(void);

/** ???Y?????? */
float getAccelY(void);

/** ???Z?????? */
float getAccelZ(void);

/** ?????????????? */
uint8_t getAccelAccuracy(void);

/** ???X?????????? */
float getLinAccelX(void);

/** ???Y?????????? */
float getLinAccelY(void);

/** ???Z?????????? */
float getLinAccelZ(void);

/** ????????????????? */
uint8_t getLinAccelAccuracy(void);

/** ???X???????????? */
float getGyroX(void);

/** ???Y???????????? */
float getGyroY(void);

/** ???Z???????????? */
float getGyroZ(void);

/** ??????????????? */
uint8_t getGyroAccuracy(void);

/** ???X??????????? */
float getMagX(void);

/** ???Y??????????? */
float getMagY(void);

/** ???Z??????????? */
float getMagZ(void);

/** ??????????????? */
uint8_t getMagAccuracy(void);

/** ��??????? */
void calibrateAccelerometer(void);

/** ��??????? */
void calibrateGyro(void);

/** ��??????? */
void calibrateMagnetometer(void);

/** ��????????? */
void calibratePlanarAccelerometer(void);

/** ��????��????? */
void calibrateAll(void);

/** ????��????? */
void endCalibration(void);

/** ????��????? */
void saveCalibration(void);

/** ???????????
  * @return ???????
  */
uint16_t getStepCount(void);

/** ???????????
  * @return ???????
  */
uint8_t getStabilityClassifier(void);

/** ?????????
  * @return ??????
  */
uint8_t getActivityClassifier(void);

/** ?????????????????
  * @param reportID ????ID
  * @param timeBetweenReports ?????????????
  * @param specificConfig ??????��???
  */
void setFeatureCommand(uint8_t reportID, uint32_t timeBetweenReports, uint32_t specificConfig);

/** ????????
  * @param command ????ID
  */
void sendCommand(uint8_t command);

/** ????��?????
  * @param thingToCalibrate ?��??????
  */
void sendCalibrateCommand(uint8_t thingToCalibrate);

/** ???FRS?????Q1?
  * @param recordID FRS???ID
  * @return Q1?
  */
int16_t getQ1(uint16_t recordID);

/** ???FRS?????Q2?
  * @param recordID FRS???ID
  * @return Q2?
  */
int16_t getQ2(uint16_t recordID);

/** ???FRS?????Q3?
  * @param recordID FRS???ID
  * @return Q3?
  */
int16_t getQ3(uint16_t recordID);

/** ??????????????
  * @param recordID FRS???ID
  * @return ??????????????
  */
float getResolution(uint16_t recordID);

/** ?????????????
  * @param recordID FRS???ID
  * @return ?????????????
  */
float getRange(uint16_t recordID);

/** ???FRS??????
  * @param recordID FRS???ID
  * @param wordNumber ????
  * @return ?????32��????
  */
uint32_t readFRSword(uint16_t recordID, uint8_t wordNumber);

/** ??????FRS????
  * @param recordID FRS???ID
  * @param readOffset ????????
  * @param blockSize ??????��
  */
void frsReadRequest(uint16_t recordID, uint16_t readOffset, uint16_t blockSize);

/** ???FRS????
  * @param recordID FRS???ID
  * @param startLocation ???��??
  * @param wordsToRead ??????????
  * @return 1????????0??????
  */
uint8_t readFRSdata(uint16_t recordID, uint8_t startLocation, uint8_t wordsToRead);

/** ???????????????
  * @param timeBetweenReports ?????????????
  */
void enableRotationVector(uint32_t timeBetweenReports);

/** ??????????????????
  * @param timeBetweenReports ?????????????
  */
void enableGameRotationVector(uint32_t timeBetweenReports);

/** ???��????????
  * @param timeBetweenReports ?????????????
  */
void enableAccelerometer(uint32_t timeBetweenReports);

/** ????????????????
  * @param timeBetweenReports ?????????????
  */
void enableLinearAccelerometer(uint32_t timeBetweenReports);

/** ?????????????
  * @param timeBetweenReports ?????????????
  */
void enableGyro(uint32_t timeBetweenReports);

/** ????????????
  * @param timeBetweenReports ?????????????
  */
void enableMagnetometer(uint32_t timeBetweenReports);

/** ???��?????????????
  * @param timeBetweenReports ?????????????
  */
void enableStepCounter(uint32_t timeBetweenReports);

/** ??????????????????
  * @param timeBetweenReports ?????????????
  */
void enableStabilityClassifier(uint32_t timeBetweenReports);

/** ???????????
  * @param ????
  */
void QuaternionToEulerAngles(float quatI, float quatJ, float quatK, float quatReal,
                            float *roll, float *pitch, float *yaw);

/* Note: my_bno080_init() and bno080_task() are declared in bno08x_app.h */

/**
  * @}
  */

#endif /* __BNO08X_HAL_H_ */
