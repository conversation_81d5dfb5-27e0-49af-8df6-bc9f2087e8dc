///**
// * @file bno08x_hardware_reset_example.h
// * @brief BNO08X硬件复位功能使用示例头文件
// */

//#ifndef __BNO08X_HARDWARE_RESET_EXAMPLE_H_
//#define __BNO08X_HARDWARE_RESET_EXAMPLE_H_

//#include "mydefine.h"

///**
// * @brief BNO08X硬件复位示例函数
// * @note 演示硬件复位的完整流程
// */
//void bno08x_hardware_reset_example(void);

///**
// * @brief BNO08X传感器异常恢复示例
// * @note 演示在传感器异常时如何使用硬件复位进行恢复
// */
//void bno08x_error_recovery_example(void);

///**
// * @brief 硬件复位时序测试
// * @note 测试不同复位时序的效果
// */
//void bno08x_reset_timing_test(void);

//#endif /* __BNO08X_HARDWARE_RESET_EXAMPLE_H_ */
