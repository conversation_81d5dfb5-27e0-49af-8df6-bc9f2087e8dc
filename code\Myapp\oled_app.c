#include "oled_app.h"

//extern I2C_HandleTypeDef hi2c2;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[128]; // 减小缓冲区大小，避免栈溢出
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  // 参数有效性检查
  if (format == NULL) {
    return -1;
  }

  va_start(arg, format);
  // 安全格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  // 检查格式化是否成功
  if (len < 0 || len >= sizeof(buffer)) {
    // 格式化失败或缓冲区溢出，显示错误信息
    OLED_ShowStr(x, y, "ERR", 8);
    return -1;
  }

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}


void my_oled_init(void)
{
	OLED_Init();
}	

// 简化的OLED测试任务
void oled_task(void)
{
	static uint8_t test_mode = 0;
	static uint32_t last_switch = 0;
	uint32_t current_time = HAL_GetTick();

	// 每3秒切换一次测试模式
	if (current_time - last_switch > 3000) {
		test_mode = (test_mode + 1) % 4;
		last_switch = current_time;
	}

	switch(test_mode) {
		case 0:
			// 基础文本测试
			oled_printf(0, 0, "OLED Test");
			oled_printf(0, 1, "Mode: %d", test_mode);
			oled_printf(0, 2, "Count: %d", count);
			break;

		case 1:
			// 数字测试
			oled_printf(0, 0, "Numbers");
			oled_printf(0, 1, "Int: %d", 12345);
			oled_printf(0, 2, "Time: %lu", current_time);
			break;

		case 2:
			// 浮点数测试
			oled_printf(0, 0, "Float Test");
			oled_printf(0, 1, "Pi: %.2f", 3.14159f);
			oled_printf(0, 2, "E: %.2f", 2.71828f);
			break;

		case 3:
			// BNO08X数据测试（如果可用）
			{
				float roll = get_roll();
				float pitch = get_pitch();
				float yaw = get_yaw();

				if (isnan(roll) || isnan(pitch) || isnan(yaw)) {
					oled_printf(0, 0, "BNO08X ERR");
					oled_printf(0, 1, "No Data");
					oled_printf(0, 2, "cnt:%d", count);
				} else {
					oled_printf(0, 0, "R:%.1f", roll);
					oled_printf(0, 1, "P:%.1f", pitch);
					oled_printf(0, 2, "Y:%.1f", yaw);
				}
			}
			break;
	}
}

