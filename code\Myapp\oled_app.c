#include "oled_app.h"

//extern I2C_HandleTypeDef hi2c2;

/**
 * @brief	ʹ������printf�ķ�ʽ��ʾ�ַ�������ʾ6x8��С��ASCII�ַ�
 * @param x  Character position on the X-axis  range��0 - 127
 * @param y  Character position on the Y-axis  range��0 - 3
 * ���磺oled_printf(0, 0, "Data = %d", dat);
 **/
int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[128]; // 减小缓冲区大小，避免栈溢出
  va_list arg;      // 可变参数列表
  int len;          // 返回字符串长度

  // 参数有效性检查
  if (format == NULL) {
    return -1;
  }

  va_start(arg, format);
  // 安全格式化字符串到 buffer
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  // 检查格式化是否成功
  if (len < 0 || len >= sizeof(buffer)) {
    // 格式化失败或缓冲区溢出，显示错误信息
    OLED_ShowStr(x, y, "ERR", 8);
    return -1;
  }

  OLED_ShowStr(x, y, buffer, 8);
  return len;
}


void my_oled_init(void)
{
	OLED_Init();
}	

void oled_task(void)
{
	// 获取三个姿态角
	float roll = get_roll();
	float pitch = get_pitch();
	float yaw = get_yaw();

	// 数据有效性检查
	if (isnan(roll) || isnan(pitch) || isnan(yaw) ||
	    isinf(roll) || isinf(pitch) || isinf(yaw)) {
		// 如果数据无效，显示错误信息
		oled_printf(0, 0, "BNO08X ERR");
		oled_printf(0, 1, "Check I2C");
		oled_printf(0, 2, "count=%d", count);
		return;
	}

	// 限制数值范围，避免显示异常
	if (roll > 999.9f) roll = 999.9f;
	if (roll < -999.9f) roll = -999.9f;
	if (pitch > 999.9f) pitch = 999.9f;
	if (pitch < -999.9f) pitch = -999.9f;
	if (yaw > 9999.9f) yaw = 9999.9f;
	if (yaw < -9999.9f) yaw = -9999.9f;

	// 显示三个姿态角
	oled_printf(0, 0, "R:%.1f", roll);    // 滚转角
	oled_printf(64, 0, "P:%.1f", pitch);  // 俯仰角
	oled_printf(0, 1, "Y:%.1f", yaw);     // 偏航角
	oled_printf(0, 2, "cnt:%d", count);   // 计数器
}

